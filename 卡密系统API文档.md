# 卡密系统API文档

## 概述

卡密系统为用户提供了通过兑换卡密获取特定权益的功能，支持次数权益和会员时长权益两种类型。

### 权益类型

1. **次数权益 (quota)**: 为用户提供指定次数的功能使用权限
2. **会员时长权益 (membership)**: 为用户延长会员有效期

### 权益优先级

1. **会员时长权益** (最高优先级) - 享受无限制使用
2. **卡密次数权益** - 按次数消费
3. **免费用户限制** - 原有的免费额度限制

## 用户端API

### 1. 兑换卡密

**接口**: `POST /cards/redeem`

**描述**: 用户兑换卡密获取权益

**请求头**:
```
Authorization: Bearer <token>
Content-Type: application/json
```

**请求体**:
```json
{
  "card_code": "ABCD1234EFGH5678"
}
```

**响应示例**:

成功兑换:
```json
{
  "success": true,
  "message": "卡密兑换成功",
  "benefit_type": "quota",
  "benefit_value": 10,
  "benefit_unit": "times",
  "benefit_description": "简历导出次数权益"
}
```

失败响应:
```json
{
  "success": false,
  "message": "卡密不存在"
}
```

### 2. 获取用户权益汇总

**接口**: `GET /cards/benefits`

**描述**: 获取用户当前的权益汇总信息

**请求头**:
```
Authorization: Bearer <token>
```

**响应示例**:
```json
{
  "quota_benefits": [
    {
      "id": 1,
      "benefit_type": "quota",
      "remaining_quota": 5,
      "expires_at": "2024-01-31T23:59:59",
      "source": "card",
      "source_id": 123,
      "is_active": true,
      "created_at": "2024-01-01T10:00:00"
    }
  ],
  "total_quota": 5,
  "membership_benefits": [],
  "has_active_membership": false,
  "membership_expires_at": null
}
```

### 3. 验证卡密

**接口**: `GET /cards/validate/{card_code}`

**描述**: 验证卡密有效性（不实际兑换）

**请求头**:
```
Authorization: Bearer <token>
```

**响应示例**:

有效卡密:
```json
{
  "valid": true,
  "message": "卡密有效",
  "benefit_type": "quota",
  "benefit_value": 10,
  "benefit_unit": "times",
  "benefit_description": "简历导出次数权益",
  "expires_at": "2024-12-31T23:59:59"
}
```

无效卡密:
```json
{
  "valid": false,
  "message": "卡密不存在"
}
```

### 4. 检查次数权益可用性

**接口**: `GET /cards/quota/check`

**描述**: 检查用户是否有足够的次数权益

**请求参数**:
- `feature_name` (string): 功能名称
- `quota_needed` (int): 需要的次数，默认为1

**请求头**:
```
Authorization: Bearer <token>
```

**响应示例**:

会员用户:
```json
{
  "available": true,
  "source": "membership",
  "message": "会员权益可用",
  "membership_expires_at": "2024-12-31T23:59:59"
}
```

次数权益用户:
```json
{
  "available": true,
  "source": "card_quota",
  "message": "卡密次数权益，剩余 5 次",
  "remaining_quota": 5
}
```

权益不足:
```json
{
  "available": false,
  "source": null,
  "message": "权益不足，需要 1 次，剩余 0 次",
  "remaining_quota": 0
}
```

### 5. 消费次数权益

**接口**: `POST /cards/quota/consume`

**描述**: 消费用户的次数权益

**请求参数**:
- `feature_name` (string): 功能名称
- `quota_needed` (int): 需要消费的次数，默认为1

**请求头**:
```
Authorization: Bearer <token>
```

**响应示例**:

会员用户（不消费次数）:
```json
{
  "consumed": false,
  "source": "membership",
  "message": "使用会员权益，无需消费次数",
  "remaining_quota": 5
}
```

次数权益消费成功:
```json
{
  "consumed": true,
  "source": "card_quota",
  "message": "成功消费 1 次权益",
  "remaining_quota": 4
}
```

次数权益不足:
```json
{
  "consumed": false,
  "source": "card_quota",
  "message": "次数权益不足",
  "remaining_quota": 0
}
```

### 6. 获取兑换历史

**接口**: `GET /cards/history`

**描述**: 获取用户的卡密兑换历史

**请求参数**:
- `page` (int): 页码，默认为1
- `page_size` (int): 每页数量，默认为20

**请求头**:
```
Authorization: Bearer <token>
```

**响应示例**:
```json
{
  "items": [
    {
      "id": 1,
      "card_id": 123,
      "user_id": 456,
      "benefit_type": "quota",
      "benefit_value": 10,
      "benefit_unit": "times",
      "ip_address": "***********",
      "used_at": "2024-01-01T10:00:00"
    }
  ],
  "total": 1,
  "page": 1,
  "page_size": 20,
  "total_pages": 1
}
```

## 增强权限检查API

### 1. 检查功能权限

**接口**: `GET /permissions/check/{feature}`

**描述**: 检查功能权限，集成卡密权益和会员权益

**请求参数**:
- `quota_needed` (int): 需要的次数，默认为1

**请求头**:
```
Authorization: Bearer <token>
```

**响应示例**:

会员权益:
```json
{
  "has_permission": true,
  "source": "membership",
  "reason": "会员权益",
  "is_member": true,
  "membership_expires_at": "2024-12-31T23:59:59",
  "usage_info": {
    "limit": -1,
    "used": 0,
    "remaining": -1,
    "source": "membership"
  }
}
```

卡密次数权益:
```json
{
  "has_permission": true,
  "source": "card_quota",
  "reason": "卡密次数权益，剩余 5 次",
  "is_member": false,
  "quota_benefits": [...],
  "usage_info": {
    "limit": 5,
    "used": 0,
    "remaining": 5,
    "source": "card_quota"
  }
}
```

### 2. 消费功能权限

**接口**: `POST /permissions/consume/{feature}`

**描述**: 消费功能权限，根据权益来源自动选择消费方式

**请求参数**:
- `quota_needed` (int): 需要的次数，默认为1
- `template_id` (string): 模板ID（可选）

**请求头**:
```
Authorization: Bearer <token>
```

**响应示例**:

会员权益使用:
```json
{
  "success": true,
  "source": "membership",
  "message": "使用会员权益",
  "consumed_quota": 0,
  "remaining_quota": -1
}
```

卡密次数权益消费:
```json
{
  "success": true,
  "source": "card_quota",
  "message": "消费卡密次数权益 1 次",
  "consumed_quota": 1,
  "remaining_quota": 4
}
```

### 3. 获取用户权限汇总

**接口**: `GET /permissions/summary`

**描述**: 获取用户完整的权限汇总信息

**请求头**:
```
Authorization: Bearer <token>
```

**响应示例**:
```json
{
  "user_id": 123,
  "membership_info": {
    "is_member": false,
    "membership_expires_at": null
  },
  "card_benefits": {
    "total_quota": 5,
    "quota_benefits": [...],
    "membership_benefits": [],
    "has_active_membership": false,
    "membership_expires_at": null
  },
  "feature_permissions": {
    "resume_export": {
      "has_permission": true,
      "source": "card_quota",
      "reason": "卡密次数权益，剩余 5 次"
    },
    "idphoto_generate": {
      "has_permission": false,
      "source": "free_limit",
      "reason": "今日使用次数已达上限"
    }
  },
  "summary": {
    "primary_benefit_source": "card_quota",
    "unlimited_access": false,
    "total_available_quota": 5
  }
}
```

### 4. 获取详细权益信息

**接口**: `GET /permissions/benefits/detailed`

**描述**: 获取详细的权益信息，包括使用统计

**请求头**:
```
Authorization: Bearer <token>
```

**响应示例**:
```json
{
  "membership_info": {
    "is_member": false
  },
  "card_benefits": {
    "total_quota": 5,
    "quota_benefits": [
      {
        "id": 1,
        "benefit_type": "quota",
        "remaining_quota": 5,
        "expires_at": "2024-12-31T23:59:59",
        "source": "card",
        "source_id": 123,
        "created_at": "2024-01-01T10:00:00"
      }
    ],
    "membership_benefits": [],
    "has_active_membership": false,
    "membership_expires_at": null
  },
  "usage_statistics": {
    "today": {
      "export_pdf": 2,
      "generate_idphoto": 1
    },
    "this_month": {
      "export_pdf": 15,
      "generate_idphoto": 8
    }
  },
  "benefit_priority": {
    "primary_source": "card_quota",
    "description": "当前使用卡密次数权益，剩余 5 次"
  }
}
```

### 5. 获取可用功能列表

**接口**: `GET /permissions/features/available`

**描述**: 获取用户当前可用的功能列表

**请求头**:
```
Authorization: Bearer <token>
```

**响应示例**:
```json
{
  "available_features": [
    {
      "feature": "resume_export",
      "source": "card_quota",
      "reason": "卡密次数权益，剩余 5 次",
      "usage_info": {
        "limit": 5,
        "used": 0,
        "remaining": 5,
        "source": "card_quota"
      }
    }
  ],
  "total_available": 1
}
```

## 管理员API

### 1. 创建卡密批次

**接口**: `POST /admin/cards/batches`

**描述**: 管理员创建卡密批次并生成卡密

**请求头**:
```
Authorization: Bearer <admin_token>
Content-Type: application/json
```

**请求体**:
```json
{
  "batch_name": "新年活动卡密",
  "benefit_type": "quota",
  "benefit_value": 10,
  "benefit_unit": "times",
  "card_count": 100,
  "benefit_description": "简历导出次数权益",
  "expires_at": "2024-12-31T23:59:59",
  "notes": "新年活动专用卡密"
}
```

**响应示例**:
```json
{
  "batch_id": 1,
  "batch_code": "BATCH_20240101120000_ABCD",
  "generated_count": 100,
  "card_codes": [
    "ABCD1234EFGH5678",
    "IJKL9876MNOP5432",
    "..."
  ]
}
```

### 2. 获取卡密批次列表

**接口**: `GET /admin/cards/batches`

**描述**: 获取卡密批次列表

**请求参数**:
- `page` (int): 页码，默认为1
- `page_size` (int): 每页数量，默认为20
- `status` (string): 状态筛选，可选值: active, disabled
- `benefit_type` (string): 权益类型筛选，可选值: quota, membership

**请求头**:
```
Authorization: Bearer <admin_token>
```

**响应示例**:
```json
{
  "items": [
    {
      "id": 1,
      "batch_name": "新年活动卡密",
      "batch_code": "BATCH_20240101120000_ABCD",
      "benefit_type": "quota",
      "benefit_value": 10,
      "benefit_unit": "times",
      "total_cards": 100,
      "used_cards": 25,
      "status": "active",
      "created_by": 1,
      "created_at": "2024-01-01T12:00:00",
      "updated_at": "2024-01-01T12:00:00"
    }
  ],
  "total": 1,
  "page": 1,
  "page_size": 20,
  "total_pages": 1
}
```

### 3. 获取卡密批次详情

**接口**: `GET /admin/cards/batches/{batch_id}`

**描述**: 获取指定卡密批次的详细信息

**请求头**:
```
Authorization: Bearer <admin_token>
```

**响应示例**:
```json
{
  "id": 1,
  "batch_name": "新年活动卡密",
  "batch_code": "BATCH_20240101120000_ABCD",
  "benefit_type": "quota",
  "benefit_value": 10,
  "benefit_unit": "times",
  "benefit_description": "简历导出次数权益",
  "total_cards": 100,
  "used_cards": 25,
  "status": "active",
  "expires_at": "2024-12-31T23:59:59",
  "created_by": 1,
  "notes": "新年活动专用卡密",
  "created_at": "2024-01-01T12:00:00",
  "updated_at": "2024-01-01T12:00:00"
}
```

### 4. 更新卡密批次

**接口**: `PUT /admin/cards/batches/{batch_id}`

**描述**: 更新卡密批次信息

**请求头**:
```
Authorization: Bearer <admin_token>
Content-Type: application/json
```

**请求体**:
```json
{
  "status": "disabled",
  "notes": "活动结束，批次已禁用"
}
```

**响应示例**:
```json
{
  "message": "批次更新成功"
}
```

### 5. 禁用卡密批次

**接口**: `POST /admin/cards/batches/{batch_id}/disable`

**描述**: 禁用卡密批次及其下所有未使用的卡密

**请求头**:
```
Authorization: Bearer <admin_token>
```

**响应示例**:
```json
{
  "message": "批次禁用成功"
}
```

### 6. 获取卡密列表

**接口**: `GET /admin/cards/cards`

**描述**: 获取卡密列表

**请求参数**:
- `batch_id` (int): 批次ID筛选（可选）
- `status` (string): 状态筛选（可选）
- `page` (int): 页码，默认为1
- `page_size` (int): 每页数量，默认为20

**请求头**:
```
Authorization: Bearer <admin_token>
```

**响应示例**:
```json
{
  "items": [
    {
      "id": 1,
      "batch_id": 1,
      "card_code": "ABCD1234EFGH5678",
      "status": "used",
      "used_by": 123,
      "used_at": "2024-01-15T10:30:00",
      "used_ip": "***********",
      "expires_at": "2024-12-31T23:59:59",
      "created_at": "2024-01-01T12:00:00"
    }
  ],
  "total": 100,
  "page": 1,
  "page_size": 20,
  "total_pages": 5
}
```

### 7. 获取卡密统计信息

**接口**: `GET /admin/cards/statistics`

**描述**: 获取卡密系统的统计信息

**请求头**:
```
Authorization: Bearer <admin_token>
```

**响应示例**:
```json
{
  "total_batches": 5,
  "total_cards": 500,
  "used_cards": 125,
  "unused_cards": 375,
  "overall_usage_rate": 25.0,
  "recent_batches": [
    {
      "id": 1,
      "batch_name": "新年活动卡密",
      "batch_code": "BATCH_20240101120000_ABCD",
      "benefit_type": "quota",
      "benefit_value": 10,
      "benefit_unit": "times",
      "total_cards": 100,
      "used_cards": 25,
      "unused_cards": 75,
      "usage_rate": 25.0,
      "status": "active",
      "created_at": "2024-01-01T12:00:00"
    }
  ]
}
```

## 命令行工具

### 安装和使用

卡密管理命令行工具位于项目根目录的 `card_management.py` 文件。

### 基本命令

#### 1. 创建卡密批次

```bash
python card_management.py create \
  --name "测试批次" \
  --type quota \
  --value 10 \
  --unit times \
  --count 100 \
  --expires 30 \
  --description "测试用卡密" \
  --notes "仅供测试使用"
```

参数说明:
- `--name`: 批次名称（必需）
- `--type`: 权益类型，quota 或 membership（必需）
- `--value`: 权益数值（必需）
- `--unit`: 权益单位，times/days/months（必需）
- `--count`: 生成卡密数量（必需）
- `--expires`: 过期天数（可选）
- `--description`: 权益描述（可选）
- `--notes`: 备注信息（可选）

#### 2. 列出卡密批次

```bash
# 列出所有批次
python card_management.py list

# 按状态筛选
python card_management.py list --status active

# 限制显示数量
python card_management.py list --limit 10
```

#### 3. 查看统计信息

```bash
python card_management.py stats
```

#### 4. 禁用批次

```bash
python card_management.py disable 1
```

#### 5. 搜索卡密

```bash
# 按卡密码或批次名称搜索
python card_management.py search "ABCD"

# 限制搜索结果数量
python card_management.py search "测试" --limit 5
```

#### 6. 导出批次卡密

```bash
# 导出为CSV和JSON格式
python card_management.py export 1

# 只导出CSV格式
python card_management.py export 1 --format csv

# 只导出JSON格式
python card_management.py export 1 --format json
```

#### 7. 验证卡密

```bash
python card_management.py verify ABCD1234EFGH5678
```

### 输出示例

#### 统计信息输出

```
📈 卡密系统统计信息
==================================================

📦 批次统计:
   总批次数: 5
   活跃批次: 4
   禁用批次: 1

🎫 卡密统计:
   总卡密数: 500
   未使用: 375
   已使用: 125
   已过期: 0
   已禁用: 0
   使用率: 25.00%

🎁 权益类型统计:
   quota: 4 批次, 400 张卡密
   membership: 1 批次, 100 张卡密

📅 最近7天使用情况:
   2024-01-15: 25 张
   2024-01-14: 18 张
   2024-01-13: 12 张
```

#### 批次列表输出

```
📊 卡密批次列表:
ID | 批次名称     | 批次编码                 | 权益       | 总数 | 已用 | 剩余 | 使用率  | 状态     | 过期时间
---------------------------------------------------------------------------------
1  | 新年活动卡密 | BATCH_20240101120000 | 10 times | 100| 25 | 75 | 25.0% | active | 2024-12-31
2  | 春节特惠     | BATCH_20240201120000 | 30 days  | 50 | 10 | 40 | 20.0% | active | 永不过期
```

## 错误码说明

### 通用错误码

- `400`: 请求参数错误
- `401`: 未授权，需要登录
- `403`: 权限不足
- `404`: 资源不存在
- `500`: 服务器内部错误

### 卡密相关错误

- `CARD_NOT_FOUND`: 卡密不存在
- `CARD_ALREADY_USED`: 卡密已被使用
- `CARD_EXPIRED`: 卡密已过期
- `CARD_DISABLED`: 卡密已禁用
- `BATCH_DISABLED`: 批次已禁用
- `BATCH_EXPIRED`: 批次已过期
- `INSUFFICIENT_QUOTA`: 次数权益不足

### 权限相关错误

- `PERMISSION_DENIED`: 权限被拒绝
- `QUOTA_EXHAUSTED`: 配额已用完
- `MEMBERSHIP_REQUIRED`: 需要会员权限
- `FEATURE_NOT_AVAILABLE`: 功能不可用

## 使用场景示例

### 场景1: 用户兑换卡密

1. 用户输入卡密码
2. 调用 `/cards/validate/{card_code}` 验证卡密
3. 确认后调用 `/cards/redeem` 兑换卡密
4. 调用 `/cards/benefits` 查看获得的权益

### 场景2: 功能使用权限检查

1. 用户尝试使用某功能
2. 调用 `/permissions/check/{feature}` 检查权限
3. 如有权限，调用 `/permissions/consume/{feature}` 消费权益
4. 执行功能逻辑

### 场景3: 管理员批量生成卡密

1. 调用 `/admin/cards/batches` 创建批次
2. 获取生成的卡密列表
3. 将卡密分发给用户
4. 通过 `/admin/cards/statistics` 监控使用情况

### 场景4: 命令行批量管理

1. 使用 `card_management.py create` 创建批次
2. 使用 `card_management.py export` 导出卡密
3. 使用 `card_management.py stats` 查看统计
4. 使用 `card_management.py disable` 禁用过期批次
```
