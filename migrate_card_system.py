"""
数据库迁移脚本：添加卡密系统相关表
"""
import mysql.connector
from mysql.connector import Error
import logging
from datetime import datetime

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 数据库连接配置
DB_CONFIG = {
    'host': 'localhost',
    'database': 'resume_service',
    'user': 'resume_user',
    'password': 'Resume123!'
}

def create_card_tables():
    """创建卡密系统相关表"""
    connection = None
    cursor = None
    
    try:
        # 连接数据库
        connection = mysql.connector.connect(**DB_CONFIG)
        cursor = connection.cursor()
        
        logger.info("连接数据库成功")
        
        # 检查表是否已存在
        tables_to_check = ['card_batches', 'cards', 'card_usage_records', 'user_benefits']
        existing_tables = []
        
        for table_name in tables_to_check:
            check_sql = """
            SELECT COUNT(*) 
            FROM INFORMATION_SCHEMA.TABLES 
            WHERE TABLE_SCHEMA = 'resume_service' 
            AND TABLE_NAME = %s
            """
            cursor.execute(check_sql, (table_name,))
            if cursor.fetchone()[0] > 0:
                existing_tables.append(table_name)
        
        if existing_tables:
            logger.info(f"以下表已存在，跳过创建: {', '.join(existing_tables)}")
            # 如果所有表都存在，直接返回
            if len(existing_tables) == len(tables_to_check):
                return True
        
        # 创建卡密批次表
        if 'card_batches' not in existing_tables:
            create_card_batches_sql = """
            CREATE TABLE card_batches (
                id INT AUTO_INCREMENT PRIMARY KEY,
                merchant_id INT NULL,
                batch_name VARCHAR(100) NOT NULL COMMENT '批次名称',
                batch_code VARCHAR(50) NOT NULL UNIQUE COMMENT '批次编码',
                benefit_type VARCHAR(20) NOT NULL COMMENT '权益类型：quota/membership',
                benefit_value INT NOT NULL COMMENT '权益数值',
                benefit_unit VARCHAR(20) NOT NULL COMMENT '权益单位：times/days/months',
                benefit_description TEXT NULL COMMENT '权益描述',
                total_cards INT DEFAULT 0 NOT NULL COMMENT '总卡密数量',
                used_cards INT DEFAULT 0 NOT NULL COMMENT '已使用数量',
                status VARCHAR(20) DEFAULT 'active' NOT NULL COMMENT '批次状态',
                expires_at TIMESTAMP NULL COMMENT '批次过期时间',
                created_by INT NULL COMMENT '创建者用户ID',
                notes TEXT NULL COMMENT '备注信息',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                INDEX idx_batch_merchant_status (merchant_id, status),
                INDEX idx_batch_benefit_type (benefit_type),
                INDEX idx_batch_created_at (created_at),
                INDEX idx_batch_code (batch_code),
                FOREIGN KEY (merchant_id) REFERENCES merchants(id)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='卡密批次表'
            """
            cursor.execute(create_card_batches_sql)
            logger.info("创建card_batches表成功")
        
        # 创建卡密表
        if 'cards' not in existing_tables:
            create_cards_sql = """
            CREATE TABLE cards (
                id INT AUTO_INCREMENT PRIMARY KEY,
                batch_id INT NOT NULL COMMENT '批次ID',
                card_code VARCHAR(32) NOT NULL UNIQUE COMMENT '卡密码',
                status VARCHAR(20) DEFAULT 'unused' NOT NULL COMMENT '卡密状态',
                used_by INT NULL COMMENT '使用者用户ID',
                used_at TIMESTAMP NULL COMMENT '使用时间',
                used_ip VARCHAR(45) NULL COMMENT '使用IP地址',
                expires_at TIMESTAMP NULL COMMENT '卡密过期时间',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                INDEX idx_card_batch_status (batch_id, status),
                INDEX idx_card_used_by (used_by),
                INDEX idx_card_expires_at (expires_at),
                INDEX idx_card_code (card_code),
                FOREIGN KEY (batch_id) REFERENCES card_batches(id) ON DELETE CASCADE,
                FOREIGN KEY (used_by) REFERENCES users(id)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='卡密表'
            """
            cursor.execute(create_cards_sql)
            logger.info("创建cards表成功")
        
        # 创建卡密使用记录表
        if 'card_usage_records' not in existing_tables:
            create_usage_records_sql = """
            CREATE TABLE card_usage_records (
                id INT AUTO_INCREMENT PRIMARY KEY,
                card_id INT NOT NULL COMMENT '卡密ID',
                user_id INT NOT NULL COMMENT '用户ID',
                benefit_type VARCHAR(20) NOT NULL COMMENT '权益类型',
                benefit_value INT NOT NULL COMMENT '权益数值',
                benefit_unit VARCHAR(20) NOT NULL COMMENT '权益单位',
                ip_address VARCHAR(45) NULL COMMENT '使用IP地址',
                user_agent TEXT NULL COMMENT '用户代理信息',
                business_context TEXT NULL COMMENT '业务上下文',
                used_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '使用时间',
                INDEX idx_usage_user_benefit (user_id, benefit_type),
                INDEX idx_usage_card_user (card_id, user_id),
                INDEX idx_usage_used_at (used_at),
                FOREIGN KEY (card_id) REFERENCES cards(id) ON DELETE CASCADE,
                FOREIGN KEY (user_id) REFERENCES users(id)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='卡密使用记录表'
            """
            cursor.execute(create_usage_records_sql)
            logger.info("创建card_usage_records表成功")
        
        # 创建用户权益余额表
        if 'user_benefits' not in existing_tables:
            create_user_benefits_sql = """
            CREATE TABLE user_benefits (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT NOT NULL COMMENT '用户ID',
                benefit_type VARCHAR(20) NOT NULL COMMENT '权益类型',
                remaining_quota INT DEFAULT 0 NOT NULL COMMENT '剩余次数',
                expires_at TIMESTAMP NULL COMMENT '过期时间',
                source VARCHAR(20) DEFAULT 'card' NOT NULL COMMENT '来源：card/purchase',
                source_id INT NULL COMMENT '来源ID（卡密ID或订单ID）',
                is_active BOOLEAN DEFAULT TRUE NOT NULL COMMENT '是否有效',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                INDEX idx_benefit_user_type (user_id, benefit_type),
                INDEX idx_benefit_expires_at (expires_at),
                INDEX idx_benefit_source (source, source_id),
                INDEX idx_benefit_active (is_active),
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户权益余额表'
            """
            cursor.execute(create_user_benefits_sql)
            logger.info("创建user_benefits表成功")
        
        # 提交事务
        connection.commit()
        logger.info("卡密系统表创建完成")
        
        return True
        
    except Error as e:
        logger.error(f"数据库操作失败: {e}")
        if connection:
            connection.rollback()
        return False
        
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close()
        logger.info("数据库连接已关闭")

def rollback_card_tables():
    """回滚卡密系统表（删除表）"""
    connection = None
    cursor = None
    
    try:
        connection = mysql.connector.connect(**DB_CONFIG)
        cursor = connection.cursor()
        
        logger.info("开始回滚卡密系统表")
        
        # 按依赖关系逆序删除表
        tables_to_drop = ['user_benefits', 'card_usage_records', 'cards', 'card_batches']
        
        for table_name in tables_to_drop:
            try:
                drop_sql = f"DROP TABLE IF EXISTS {table_name}"
                cursor.execute(drop_sql)
                logger.info(f"删除表 {table_name} 成功")
            except Error as e:
                logger.warning(f"删除表 {table_name} 失败: {e}")
        
        connection.commit()
        logger.info("卡密系统表回滚完成")
        return True
        
    except Error as e:
        logger.error(f"回滚操作失败: {e}")
        if connection:
            connection.rollback()
        return False
        
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close()

def main():
    """主函数"""
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == 'rollback':
        logger.info("执行回滚操作")
        success = rollback_card_tables()
    else:
        logger.info("执行迁移操作")
        success = create_card_tables()
    
    if success:
        logger.info("操作完成")
        sys.exit(0)
    else:
        logger.error("操作失败")
        sys.exit(1)

if __name__ == "__main__":
    main()
