"""
卡密系统相关数据模式
"""
from pydantic import BaseModel, Field, validator
from typing import Optional, List
from datetime import datetime
from enum import Enum


class BenefitTypeEnum(str, Enum):
    """权益类型枚举"""
    QUOTA = "quota"          # 次数权益
    MEMBERSHIP = "membership"  # 会员时长权益


class CardStatusEnum(str, Enum):
    """卡密状态枚举"""
    UNUSED = "unused"        # 未使用
    USED = "used"           # 已使用
    EXPIRED = "expired"     # 已过期
    DISABLED = "disabled"   # 已禁用


class BatchStatusEnum(str, Enum):
    """批次状态枚举"""
    ACTIVE = "active"       # 激活
    DISABLED = "disabled"   # 禁用


class BenefitUnitEnum(str, Enum):
    """权益单位枚举"""
    TIMES = "times"         # 次数
    DAYS = "days"          # 天
    MONTHS = "months"      # 月


# 卡密批次相关模式
class CardBatchBase(BaseModel):
    """卡密批次基础信息"""
    batch_name: str = Field(..., description="批次名称", max_length=100)
    benefit_type: BenefitTypeEnum = Field(..., description="权益类型")
    benefit_value: int = Field(..., description="权益数值", gt=0)
    benefit_unit: BenefitUnitEnum = Field(..., description="权益单位")
    benefit_description: Optional[str] = Field(None, description="权益描述")
    expires_at: Optional[datetime] = Field(None, description="批次过期时间")
    notes: Optional[str] = Field(None, description="备注信息")


class CardBatchCreate(CardBatchBase):
    """创建卡密批次"""
    card_count: int = Field(..., description="生成卡密数量", gt=0, le=10000)
    
    @validator('card_count')
    def validate_card_count(cls, v):
        if v <= 0:
            raise ValueError('卡密数量必须大于0')
        if v > 10000:
            raise ValueError('单次生成卡密数量不能超过10000')
        return v


class CardBatchInfo(CardBatchBase):
    """卡密批次信息"""
    id: int
    batch_code: str
    total_cards: int
    used_cards: int
    status: BatchStatusEnum
    created_by: Optional[int]
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class CardBatchStatistics(BaseModel):
    """卡密批次统计信息"""
    id: int
    batch_name: str
    batch_code: str
    benefit_type: BenefitTypeEnum
    benefit_value: int
    benefit_unit: BenefitUnitEnum
    total_cards: int
    used_cards: int
    unused_cards: int
    usage_rate: float = Field(..., description="使用率（百分比）")
    status: BatchStatusEnum
    created_at: datetime


# 卡密相关模式
class CardBase(BaseModel):
    """卡密基础信息"""
    card_code: str = Field(..., description="卡密码", min_length=8, max_length=32)


class CardInfo(CardBase):
    """卡密信息"""
    id: int
    batch_id: int
    status: CardStatusEnum
    used_by: Optional[int]
    used_at: Optional[datetime]
    used_ip: Optional[str]
    expires_at: Optional[datetime]
    created_at: datetime

    class Config:
        from_attributes = True


class CardRedeemRequest(BaseModel):
    """卡密兑换请求"""
    card_code: str = Field(..., description="卡密码", min_length=8, max_length=32)
    
    @validator('card_code')
    def validate_card_code(cls, v):
        # 移除空格并转换为大写
        v = v.strip().upper()
        if not v:
            raise ValueError('卡密码不能为空')
        return v


class CardRedeemResponse(BaseModel):
    """卡密兑换响应"""
    success: bool
    message: str
    benefit_type: Optional[BenefitTypeEnum] = None
    benefit_value: Optional[int] = None
    benefit_unit: Optional[BenefitUnitEnum] = None
    benefit_description: Optional[str] = None


# 卡密使用记录相关模式
class CardUsageRecordInfo(BaseModel):
    """卡密使用记录信息"""
    id: int
    card_id: int
    user_id: int
    benefit_type: BenefitTypeEnum
    benefit_value: int
    benefit_unit: BenefitUnitEnum
    ip_address: Optional[str]
    used_at: datetime

    class Config:
        from_attributes = True


# 用户权益相关模式
class UserBenefitInfo(BaseModel):
    """用户权益信息"""
    id: int
    benefit_type: BenefitTypeEnum
    remaining_quota: int
    expires_at: Optional[datetime]
    source: str
    source_id: Optional[int]
    is_active: bool
    created_at: datetime

    class Config:
        from_attributes = True


class UserBenefitSummary(BaseModel):
    """用户权益汇总"""
    quota_benefits: List[UserBenefitInfo] = Field(default_factory=list, description="次数权益列表")
    total_quota: int = Field(0, description="总剩余次数")
    membership_benefits: List[UserBenefitInfo] = Field(default_factory=list, description="会员时长权益列表")
    has_active_membership: bool = Field(False, description="是否有有效会员")
    membership_expires_at: Optional[datetime] = Field(None, description="会员到期时间")


# 管理相关模式
class CardBatchListRequest(BaseModel):
    """卡密批次列表请求"""
    page: int = Field(1, description="页码", ge=1)
    page_size: int = Field(20, description="每页数量", ge=1, le=100)
    status: Optional[BatchStatusEnum] = Field(None, description="批次状态筛选")
    benefit_type: Optional[BenefitTypeEnum] = Field(None, description="权益类型筛选")


class CardListRequest(BaseModel):
    """卡密列表请求"""
    batch_id: Optional[int] = Field(None, description="批次ID筛选")
    status: Optional[CardStatusEnum] = Field(None, description="卡密状态筛选")
    page: int = Field(1, description="页码", ge=1)
    page_size: int = Field(20, description="每页数量", ge=1, le=100)


class CardBatchUpdateRequest(BaseModel):
    """卡密批次更新请求"""
    status: Optional[BatchStatusEnum] = Field(None, description="批次状态")
    notes: Optional[str] = Field(None, description="备注信息")


class CardGenerateResponse(BaseModel):
    """卡密生成响应"""
    batch_id: int
    batch_code: str
    generated_count: int
    card_codes: List[str] = Field(..., description="生成的卡密码列表")


class CardStatisticsResponse(BaseModel):
    """卡密统计响应"""
    total_batches: int = Field(..., description="总批次数")
    total_cards: int = Field(..., description="总卡密数")
    used_cards: int = Field(..., description="已使用卡密数")
    unused_cards: int = Field(..., description="未使用卡密数")
    overall_usage_rate: float = Field(..., description="总体使用率")
    recent_batches: List[CardBatchStatistics] = Field(..., description="最近批次统计")


# 分页响应模式
class PaginatedResponse(BaseModel):
    """分页响应基类"""
    total: int
    page: int
    page_size: int
    total_pages: int


class CardBatchListResponse(PaginatedResponse):
    """卡密批次列表响应"""
    items: List[CardBatchInfo]


class CardListResponse(PaginatedResponse):
    """卡密列表响应"""
    items: List[CardInfo]


class CardUsageHistoryResponse(PaginatedResponse):
    """卡密使用历史响应"""
    items: List[CardUsageRecordInfo]
