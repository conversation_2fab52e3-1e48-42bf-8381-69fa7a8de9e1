"""
商户相关Pydantic模式
"""
from pydantic import BaseModel, Field, validator
from typing import Optional, Dict, Any, List
from datetime import datetime
from app.models.merchant import MerchantStatus


class MerchantBase(BaseModel):
    """商户基础模式"""
    merchant_code: str = Field(..., description="商户代码", min_length=2, max_length=50)
    merchant_name: str = Field(..., description="商户名称", min_length=1, max_length=100)
    description: Optional[str] = Field(None, description="商户描述", max_length=500)
    contact_name: Optional[str] = Field(None, description="联系人姓名", max_length=50)
    contact_phone: Optional[str] = Field(None, description="联系电话", max_length=20)
    contact_email: Optional[str] = Field(None, description="联系邮箱", max_length=100)
    business_config: Optional[Dict[str, Any]] = Field(None, description="业务配置")


class MerchantCreate(MerchantBase):
    """创建商户模式"""
    pass


class MerchantUpdate(BaseModel):
    """更新商户模式"""
    merchant_name: Optional[str] = Field(None, description="商户名称", min_length=1, max_length=100)
    description: Optional[str] = Field(None, description="商户描述", max_length=500)
    contact_name: Optional[str] = Field(None, description="联系人姓名", max_length=50)
    contact_phone: Optional[str] = Field(None, description="联系电话", max_length=20)
    contact_email: Optional[str] = Field(None, description="联系邮箱", max_length=100)
    business_config: Optional[Dict[str, Any]] = Field(None, description="业务配置")
    status: Optional[MerchantStatus] = Field(None, description="商户状态")


class MerchantInfo(MerchantBase):
    """商户信息模式"""
    id: int = Field(..., description="商户ID")
    status: MerchantStatus = Field(..., description="商户状态")
    commission_rate: float = Field(..., description="佣金费率")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")
    
    class Config:
        from_attributes = True


class MerchantListResponse(BaseModel):
    """商户列表响应"""
    merchants: List[MerchantInfo] = Field(..., description="商户列表")
    total: int = Field(..., description="总数量")


class MerchantWeChatConfigBase(BaseModel):
    """商户微信配置基础模式"""
    app_id: str = Field(..., description="微信小程序AppID", min_length=1, max_length=50)
    app_secret: str = Field(..., description="微信小程序AppSecret", min_length=1, max_length=100)
    app_name: Optional[str] = Field(None, description="小程序名称", max_length=100)
    mch_id: str = Field(..., description="微信支付商户号", min_length=1, max_length=20)
    api_key: str = Field(..., description="微信支付API密钥", min_length=1, max_length=64)
    api_v3_key: str = Field(..., description="微信支付APIv3密钥", min_length=1, max_length=64)
    cert_path: Optional[str] = Field(None, description="API证书路径", max_length=255)
    key_path: Optional[str] = Field(None, description="API证书私钥路径", max_length=255)
    cert_serial_no: Optional[str] = Field(None, description="证书序列号", max_length=64)
    notify_url: str = Field(..., description="支付回调URL", min_length=1, max_length=255)
    is_sandbox: bool = Field(False, description="是否沙箱环境")
    order_expire_minutes: int = Field(30, description="订单过期时间(分钟)", ge=1, le=1440)
    order_prefix: str = Field("RS", description="订单号前缀", min_length=1, max_length=10)
    payment_timeout_seconds: int = Field(30, description="支付请求超时时间(秒)", ge=1, le=300)
    max_retry_times: int = Field(3, description="支付请求最大重试次数", ge=1, le=10)


class MerchantWeChatConfigCreate(MerchantWeChatConfigBase):
    """创建商户微信配置模式"""
    pass


class MerchantWeChatConfigUpdate(BaseModel):
    """更新商户微信配置模式"""
    app_secret: Optional[str] = Field(None, description="微信小程序AppSecret", min_length=1, max_length=100)
    app_name: Optional[str] = Field(None, description="小程序名称", max_length=100)
    api_key: Optional[str] = Field(None, description="微信支付API密钥", min_length=1, max_length=64)
    api_v3_key: Optional[str] = Field(None, description="微信支付APIv3密钥", min_length=1, max_length=64)
    cert_path: Optional[str] = Field(None, description="API证书路径", max_length=255)
    key_path: Optional[str] = Field(None, description="API证书私钥路径", max_length=255)
    cert_serial_no: Optional[str] = Field(None, description="证书序列号", max_length=64)
    notify_url: Optional[str] = Field(None, description="支付回调URL", min_length=1, max_length=255)
    is_sandbox: Optional[bool] = Field(None, description="是否沙箱环境")
    order_expire_minutes: Optional[int] = Field(None, description="订单过期时间(分钟)", ge=1, le=1440)
    order_prefix: Optional[str] = Field(None, description="订单号前缀", min_length=1, max_length=10)
    payment_timeout_seconds: Optional[int] = Field(None, description="支付请求超时时间(秒)", ge=1, le=300)
    max_retry_times: Optional[int] = Field(None, description="支付请求最大重试次数", ge=1, le=10)
    is_active: Optional[bool] = Field(None, description="是否启用")


class MerchantWeChatConfigInfo(MerchantWeChatConfigBase):
    """商户微信配置信息模式"""
    id: int = Field(..., description="配置ID")
    merchant_id: int = Field(..., description="商户ID")
    is_active: bool = Field(..., description="是否启用")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")
    
    # 敏感信息脱敏
    @validator('app_secret', 'api_key', 'api_v3_key')
    def mask_sensitive_data(cls, v):
        if v and len(v) > 8:
            return v[:4] + '*' * (len(v) - 8) + v[-4:]
        return v
    
    class Config:
        from_attributes = True


class MerchantConfigResponse(BaseModel):
    """商户配置响应"""
    merchant: MerchantInfo = Field(..., description="商户信息")
    wechat_config: Optional[MerchantWeChatConfigInfo] = Field(None, description="微信配置")


class MerchantStatsResponse(BaseModel):
    """商户统计响应"""
    merchant_code: str = Field(..., description="商户代码")
    total_users: int = Field(..., description="总用户数")
    total_orders: int = Field(..., description="总订单数")
    total_amount: float = Field(..., description="总交易金额")
    active_members: int = Field(..., description="活跃会员数")
    today_orders: int = Field(..., description="今日订单数")
    today_amount: float = Field(..., description="今日交易金额")


class MerchantValidationRequest(BaseModel):
    """商户验证请求"""
    merchant_code: str = Field(..., description="商户代码")
    api_secret: Optional[str] = Field(None, description="API密钥")
    request_ip: Optional[str] = Field(None, description="请求IP")


class MerchantValidationResponse(BaseModel):
    """商户验证响应"""
    valid: bool = Field(..., description="是否有效")
    merchant_info: Optional[MerchantInfo] = Field(None, description="商户信息")
    error_message: Optional[str] = Field(None, description="错误信息")
