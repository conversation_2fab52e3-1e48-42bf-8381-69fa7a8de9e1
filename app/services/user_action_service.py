"""
用户操作记录服务
专门用于记录用户的关键操作行为，供管理员统计分析使用
与会员权益消费分离，职责单一
"""

import logging
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta
from sqlalchemy.orm import Session
from sqlalchemy import func, and_

from app.models import User, UserAction

logger = logging.getLogger(__name__)


class UserActionService:
    """用户操作记录服务 - 专注于用户行为记录和统计分析"""

    # 定义操作类型分类
    ACTION_CATEGORIES = {
        "file_operations": ["download_pdf", "export_jpeg", "save_image"],
        "template_operations": ["use_template", "preview_template", "select_template"],
        "idphoto_operations": ["generate_idphoto", "upload_photo", "adjust_photo"],
        "resume_operations": ["create_resume", "edit_resume", "preview_resume"],
        "user_operations": ["login", "logout", "update_profile"],
        "system_operations": ["error_report", "feedback_submit"]
    }
    
    def get_action_category(self, action_type: str) -> str:
        """
        获取操作类型的分类

        Args:
            action_type: 操作类型

        Returns:
            操作分类
        """
        for category, actions in self.ACTION_CATEGORIES.items():
            if action_type in actions:
                return category
        return "other_operations"
    
    def record_user_action(
        self,
        db: Session,
        user_id: int,
        action_type: str,
        action_content: Optional[Dict[str, Any]] = None,
        resource_type: Optional[str] = None,
        resource_id: Optional[str] = None,
        file_size: Optional[int] = None,
        file_format: Optional[str] = None,
        operation_status: str = "completed",
        error_message: Optional[str] = None,
        client_info: Optional[Dict[str, Any]] = None,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
        template_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        记录用户操作 - 纯粹的行为记录，不涉及权益消费

        Args:
            db: 数据库会话
            user_id: 用户ID
            action_type: 操作类型
            action_content: 操作详细内容
            resource_type: 资源类型
            resource_id: 资源ID
            file_size: 文件大小
            file_format: 文件格式
            operation_status: 操作状态
            error_message: 错误信息
            client_info: 客户端信息
            ip_address: IP地址
            user_agent: 用户代理
            template_id: 模板ID（兼容字段）

        Returns:
            记录结果
        """
        try:
            # 获取操作分类
            action_category = self.get_action_category(action_type)

            # 创建用户行为记录
            user_action = UserAction(
                user_id=user_id,
                action_type=action_type,
                action_content=action_content,
                resource_type=resource_type,
                resource_id=resource_id,
                file_size=file_size,
                file_format=file_format,
                operation_status=operation_status,
                error_message=error_message,
                client_info=client_info,
                ip_address=ip_address,
                user_agent=user_agent,
                template_id=template_id
            )

            db.add(user_action)
            db.commit()
            db.refresh(user_action)

            logger.info(f"记录用户操作成功: 用户{user_id}, 操作{action_type}({action_category}), ID{user_action.id}")

            return {
                "success": True,
                "action_id": user_action.id,
                "action_category": action_category,
                "message": "操作记录成功"
            }

        except Exception as e:
            db.rollback()
            logger.error(f"记录用户操作失败: {user_id}, {action_type}, {e}")
            return {
                "success": False,
                "message": f"记录操作失败: {str(e)}"
            }
    
    def get_user_action_stats(
        self,
        db: Session,
        user_id: int,
        days: int = 30
    ) -> Dict[str, Any]:
        """
        获取用户操作统计 - 纯粹的行为统计分析

        Args:
            db: 数据库会话
            user_id: 用户ID
            days: 统计天数

        Returns:
            统计结果
        """
        try:
            # 计算统计开始时间
            start_date = datetime.now() - timedelta(days=days)

            # 统计总操作次数
            total_actions = db.query(func.count(UserAction.id)).filter(
                and_(
                    UserAction.user_id == user_id,
                    UserAction.created_at >= start_date
                )
            ).scalar() or 0

            # 统计各操作类型次数
            action_type_stats = db.query(
                UserAction.action_type,
                func.count(UserAction.id).label('count')
            ).filter(
                and_(
                    UserAction.user_id == user_id,
                    UserAction.created_at >= start_date
                )
            ).group_by(UserAction.action_type).all()

            # 统计操作状态分布
            status_stats = db.query(
                UserAction.operation_status,
                func.count(UserAction.id).label('count')
            ).filter(
                and_(
                    UserAction.user_id == user_id,
                    UserAction.created_at >= start_date
                )
            ).group_by(UserAction.operation_status).all()

            # 统计资源类型分布
            resource_type_stats = db.query(
                UserAction.resource_type,
                func.count(UserAction.id).label('count')
            ).filter(
                and_(
                    UserAction.user_id == user_id,
                    UserAction.resource_type.isnot(None),
                    UserAction.created_at >= start_date
                )
            ).group_by(UserAction.resource_type).all()

            # 按操作分类统计
            category_stats = {}
            for action_type, count in action_type_stats:
                category = self.get_action_category(action_type)
                category_stats[category] = category_stats.get(category, 0) + count

            return {
                "period_days": days,
                "start_date": start_date.date(),
                "end_date": datetime.now().date(),
                "total_actions": total_actions,
                "action_type_stats": {
                    stat.action_type: stat.count for stat in action_type_stats
                },
                "category_stats": category_stats,
                "status_stats": {
                    stat.operation_status: stat.count for stat in status_stats
                },
                "resource_type_stats": {
                    stat.resource_type: stat.count for stat in resource_type_stats
                }
            }

        except Exception as e:
            logger.error(f"获取用户操作统计失败: {user_id}, {e}")
            return {
                "period_days": days,
                "start_date": (datetime.now() - timedelta(days=days)).date(),
                "end_date": datetime.now().date(),
                "total_actions": 0,
                "action_type_stats": {},
                "category_stats": {},
                "status_stats": {},
                "resource_type_stats": {}
            }


    def get_admin_action_stats(
        self,
        db: Session,
        days: int = 30,
        limit: int = 100
    ) -> Dict[str, Any]:
        """
        获取管理员用的全局操作统计 - 用于分析用户行为趋势

        Args:
            db: 数据库会话
            days: 统计天数
            limit: 返回用户数量限制

        Returns:
            全局统计结果
        """
        try:
            # 计算统计开始时间
            start_date = datetime.now() - timedelta(days=days)

            # 统计总体数据
            total_actions = db.query(func.count(UserAction.id)).filter(
                UserAction.created_at >= start_date
            ).scalar() or 0

            total_users = db.query(func.count(func.distinct(UserAction.user_id))).filter(
                UserAction.created_at >= start_date
            ).scalar() or 0

            # 统计最活跃用户
            active_users = db.query(
                UserAction.user_id,
                func.count(UserAction.id).label('action_count')
            ).filter(
                UserAction.created_at >= start_date
            ).group_by(UserAction.user_id).order_by(
                func.count(UserAction.id).desc()
            ).limit(limit).all()

            # 统计操作类型分布
            action_type_stats = db.query(
                UserAction.action_type,
                func.count(UserAction.id).label('count')
            ).filter(
                UserAction.created_at >= start_date
            ).group_by(UserAction.action_type).order_by(
                func.count(UserAction.id).desc()
            ).all()

            # 统计每日操作趋势
            daily_stats = db.query(
                func.date(UserAction.created_at).label('date'),
                func.count(UserAction.id).label('count')
            ).filter(
                UserAction.created_at >= start_date
            ).group_by(func.date(UserAction.created_at)).order_by(
                func.date(UserAction.created_at)
            ).all()

            return {
                "period_days": days,
                "start_date": start_date.date(),
                "end_date": datetime.now().date(),
                "summary": {
                    "total_actions": total_actions,
                    "total_users": total_users,
                    "avg_actions_per_user": round(total_actions / total_users, 2) if total_users > 0 else 0
                },
                "active_users": [
                    {"user_id": user.user_id, "action_count": user.action_count}
                    for user in active_users
                ],
                "action_type_stats": {
                    stat.action_type: stat.count for stat in action_type_stats
                },
                "daily_trends": [
                    {"date": str(stat.date), "count": stat.count}
                    for stat in daily_stats
                ]
            }

        except Exception as e:
            logger.error(f"获取管理员统计失败: {e}")
            return {
                "period_days": days,
                "start_date": (datetime.now() - timedelta(days=days)).date(),
                "end_date": datetime.now().date(),
                "summary": {"total_actions": 0, "total_users": 0, "avg_actions_per_user": 0},
                "active_users": [],
                "action_type_stats": {},
                "daily_trends": []
            }


# 创建服务实例
user_action_service = UserActionService()
