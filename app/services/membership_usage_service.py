"""
会员权益消费服务
专门处理会员权益的消费记录和配额管理
与用户操作记录分离，职责单一
"""

import logging
from typing import Dict, Any, Optional
from datetime import datetime, timedelta
from sqlalchemy.orm import Session
from sqlalchemy import func, and_

from app.models.membership_usage import MembershipUsage, QuotaLimit, UsageStatus
from app.services.membership_service import membership_service

logger = logging.getLogger(__name__)


class MembershipUsageService:
    """会员权益消费服务"""
    
    def check_quota_permission(
        self,
        db: Session,
        user_id: int,
        feature_name: str,
        quota_type: str = "daily",
        requested_quota: int = 1
    ) -> Dict[str, Any]:
        """
        检查配额权限
        
        Args:
            db: 数据库会话
            user_id: 用户ID
            feature_name: 功能名称
            quota_type: 配额类型
            requested_quota: 请求的配额数量
        
        Returns:
            权限检查结果
        """
        try:
            # 获取用户会员信息
            membership_info = membership_service.get_user_membership_info(db, user_id)
            is_member = membership_info["is_member"]
            
            # 获取配额限制配置
            quota_config = self._get_quota_config(db, feature_name, quota_type)
            if not quota_config:
                return {
                    "has_permission": False,
                    "reason": "功能配额配置不存在",
                    "quota_info": {}
                }
            
            # 确定用户的配额限制
            if is_member:
                limit = quota_config.member_limit
            else:
                limit = quota_config.free_limit
            
            # -1 表示无限制
            if limit == -1:
                return {
                    "has_permission": True,
                    "reason": "无限制使用",
                    "quota_info": {
                        "limit": -1,
                        "used": 0,
                        "remaining": -1,
                        "quota_type": quota_type
                    }
                }
            
            # 检查已使用配额
            used_quota = self._get_used_quota(db, user_id, feature_name, quota_type)
            remaining = max(0, limit - used_quota)
            
            has_permission = remaining >= requested_quota
            
            return {
                "has_permission": has_permission,
                "reason": f"剩余配额: {remaining}" if has_permission else "配额已用完",
                "quota_info": {
                    "limit": limit,
                    "used": used_quota,
                    "remaining": remaining,
                    "quota_type": quota_type,
                    "requested": requested_quota
                },
                "is_member": is_member
            }
            
        except Exception as e:
            logger.error(f"检查配额权限异常: {user_id}, {feature_name}, {e}")
            return {
                "has_permission": False,
                "reason": "权限检查失败",
                "quota_info": {}
            }
    
    def consume_quota(
        self,
        db: Session,
        user_id: int,
        feature_name: str,
        consumed_quota: int = 1,
        quota_type: str = "daily",
        related_action_id: Optional[int] = None,
        resource_info: Optional[Dict[str, Any]] = None,
        business_context: Optional[Dict[str, Any]] = None,
        auto_confirm: bool = True
    ) -> Dict[str, Any]:
        """
        消费配额
        
        Args:
            db: 数据库会话
            user_id: 用户ID
            feature_name: 功能名称
            consumed_quota: 消耗配额数量
            quota_type: 配额类型
            related_action_id: 关联的用户操作ID
            resource_info: 资源信息
            business_context: 业务上下文
            auto_confirm: 是否自动确认
        
        Returns:
            消费结果
        """
        try:
            # 先检查权限
            permission_check = self.check_quota_permission(
                db, user_id, feature_name, quota_type, consumed_quota
            )
            
            if not permission_check["has_permission"]:
                return {
                    "success": False,
                    "message": permission_check["reason"],
                    "quota_info": permission_check["quota_info"]
                }
            
            # 创建使用记录
            usage_record = MembershipUsage(
                user_id=user_id,
                feature_name=feature_name,
                consumed_quota=consumed_quota,
                quota_type=quota_type,
                usage_status="confirmed" if auto_confirm else "pending",
                related_action_id=related_action_id,
                resource_info=resource_info,
                business_context=business_context,
                confirmed_at=datetime.now() if auto_confirm else None
            )
            
            db.add(usage_record)
            db.commit()
            db.refresh(usage_record)
            
            logger.info(f"配额消费成功: 用户{user_id}, 功能{feature_name}, 消费{consumed_quota}")
            
            return {
                "success": True,
                "message": "配额消费成功",
                "usage_id": usage_record.id,
                "quota_info": permission_check["quota_info"]
            }
            
        except Exception as e:
            db.rollback()
            logger.error(f"配额消费失败: {user_id}, {feature_name}, {e}")
            return {
                "success": False,
                "message": f"配额消费失败: {str(e)}"
            }
    
    def _get_quota_config(
        self,
        db: Session,
        feature_name: str,
        quota_type: str
    ) -> Optional[QuotaLimit]:
        """获取配额配置"""
        return db.query(QuotaLimit).filter(
            and_(
                QuotaLimit.feature_name == feature_name,
                QuotaLimit.quota_type == quota_type,
                QuotaLimit.is_active == True
            )
        ).order_by(QuotaLimit.priority.desc()).first()
    
    def _get_used_quota(
        self,
        db: Session,
        user_id: int,
        feature_name: str,
        quota_type: str
    ) -> int:
        """获取已使用配额"""
        # 根据配额类型计算时间范围
        now = datetime.now()
        if quota_type == "daily":
            start_time = now.replace(hour=0, minute=0, second=0, microsecond=0)
        elif quota_type == "monthly":
            start_time = now.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
        else:  # total
            start_time = datetime.min
        
        used = db.query(func.sum(MembershipUsage.consumed_quota)).filter(
            and_(
                MembershipUsage.user_id == user_id,
                MembershipUsage.feature_name == feature_name,
                MembershipUsage.quota_type == quota_type,
                MembershipUsage.usage_status == "confirmed",
                MembershipUsage.usage_date >= start_time
            )
        ).scalar() or 0
        
        return used


# 创建服务实例
membership_usage_service = MembershipUsageService()
