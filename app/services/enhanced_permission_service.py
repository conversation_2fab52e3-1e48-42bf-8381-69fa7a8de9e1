"""
增强的权限服务 - 集成卡密权益和会员权益
"""
from typing import Dict, Any, Optional
from sqlalchemy.orm import Session
from datetime import datetime
import logging

from app.models.user import User, UserAction
from app.models.payment import UserMembership
from app.services.membership_service import MembershipService
from app.services.card_service import CardService

logger = logging.getLogger(__name__)


class EnhancedPermissionService:
    """增强的权限服务，集成卡密权益和会员权益"""
    
    def __init__(self):
        self.membership_service = MembershipService()
    
    def check_feature_permission(
        self, 
        db: Session, 
        user_id: int, 
        feature: str,
        quota_needed: int = 1,
        check_usage: bool = True
    ) -> Dict[str, Any]:
        """
        检查功能权限 - 集成卡密权益和会员权益
        
        优先级：
        1. 会员时长权益（最高优先级）
        2. 卡密次数权益
        3. 原有的免费用户限制
        """
        try:
            # 获取用户权益汇总
            user_benefits = CardService.get_user_benefits(db=db, user_id=user_id)
            
            # 1. 检查会员时长权益（最高优先级）
            if user_benefits.has_active_membership:
                return {
                    "has_permission": True,
                    "source": "membership",
                    "reason": "会员权益",
                    "is_member": True,
                    "membership_expires_at": user_benefits.membership_expires_at,
                    "usage_info": {
                        "limit": -1,
                        "used": 0,
                        "remaining": -1,
                        "source": "membership"
                    }
                }
            
            # 2. 检查卡密次数权益
            if user_benefits.total_quota >= quota_needed:
                return {
                    "has_permission": True,
                    "source": "card_quota",
                    "reason": f"卡密次数权益，剩余 {user_benefits.total_quota} 次",
                    "is_member": False,
                    "quota_benefits": user_benefits.quota_benefits,
                    "usage_info": {
                        "limit": user_benefits.total_quota,
                        "used": 0,
                        "remaining": user_benefits.total_quota,
                        "source": "card_quota"
                    }
                }
            
            # 3. 回退到原有的会员权限检查逻辑
            original_result = self.membership_service.check_feature_permission(
                db=db,
                user_id=user_id,
                feature=feature,
                check_usage=check_usage
            )
            
            # 增强原有结果，添加卡密权益信息
            original_result["card_benefits"] = {
                "total_quota": user_benefits.total_quota,
                "quota_benefits_count": len(user_benefits.quota_benefits),
                "membership_benefits_count": len(user_benefits.membership_benefits)
            }
            
            return original_result
            
        except Exception as e:
            logger.error(f"检查增强功能权限异常: {user_id}, {feature}, {e}")
            return {
                "has_permission": False,
                "source": "error",
                "reason": "权限检查失败",
                "is_member": False,
                "usage_info": {}
            }
    
    def consume_feature_permission(
        self,
        db: Session,
        user_id: int,
        feature: str,
        quota_needed: int = 1,
        action_content: Optional[Dict[str, Any]] = None,
        template_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        消费功能权限
        
        根据权限来源决定消费方式：
        1. 会员权益：不消费，直接记录使用
        2. 卡密次数权益：消费次数
        3. 免费用户：按原有逻辑记录使用
        """
        try:
            # 先检查权限
            permission_result = self.check_feature_permission(
                db=db,
                user_id=user_id,
                feature=feature,
                quota_needed=quota_needed,
                check_usage=True
            )
            
            if not permission_result["has_permission"]:
                return {
                    "success": False,
                    "message": permission_result["reason"],
                    "permission_result": permission_result
                }
            
            source = permission_result.get("source", "unknown")
            
            if source == "membership":
                # 会员权益：只记录使用，不消费
                self._record_feature_usage(
                    db=db,
                    user_id=user_id,
                    feature=feature,
                    source="membership",
                    action_content=action_content,
                    template_id=template_id
                )
                
                return {
                    "success": True,
                    "source": "membership",
                    "message": "使用会员权益",
                    "consumed_quota": 0,
                    "remaining_quota": -1
                }
            
            elif source == "card_quota":
                # 卡密次数权益：消费次数
                success = CardService.consume_quota_benefit(
                    db=db,
                    user_id=user_id,
                    feature_name=feature,
                    quota_needed=quota_needed
                )
                
                if success:
                    # 记录使用
                    self._record_feature_usage(
                        db=db,
                        user_id=user_id,
                        feature=feature,
                        source="card_quota",
                        action_content=action_content,
                        template_id=template_id
                    )
                    
                    # 获取剩余权益
                    updated_benefits = CardService.get_user_benefits(db=db, user_id=user_id)
                    
                    return {
                        "success": True,
                        "source": "card_quota",
                        "message": f"消费卡密次数权益 {quota_needed} 次",
                        "consumed_quota": quota_needed,
                        "remaining_quota": updated_benefits.total_quota
                    }
                else:
                    return {
                        "success": False,
                        "source": "card_quota",
                        "message": "卡密次数权益不足",
                        "consumed_quota": 0
                    }
            
            else:
                # 原有逻辑：免费用户或其他情况
                success = self.membership_service.record_feature_usage(
                    db=db,
                    user_id=user_id,
                    feature=feature,
                    action_content=action_content,
                    template_id=template_id
                )
                
                return {
                    "success": success,
                    "source": "free_limit",
                    "message": "使用免费额度" if success else "免费额度已用完",
                    "consumed_quota": 1 if success else 0
                }
            
        except Exception as e:
            logger.error(f"消费功能权限异常: {user_id}, {feature}, {e}")
            return {
                "success": False,
                "message": f"权限消费失败: {str(e)}"
            }
    
    def _record_feature_usage(
        self,
        db: Session,
        user_id: int,
        feature: str,
        source: str,
        action_content: Optional[Dict[str, Any]] = None,
        template_id: Optional[str] = None
    ):
        """记录功能使用"""
        try:
            # 映射功能到行为类型
            action_type_mapping = {
                "resume_export": "export_pdf",
                "idphoto_generate": "generate_idphoto",
                "premium_templates": "use_premium_template",
                "priority_support": "priority_support",
                "watermark_free": "watermark_free_export"
            }
            
            action_type = action_type_mapping.get(feature, feature)
            
            # 增强action_content，添加权益来源信息
            enhanced_content = action_content or {}
            enhanced_content.update({
                "feature": feature,
                "benefit_source": source,
                "timestamp": datetime.now().isoformat()
            })
            
            # 创建用户行为记录
            user_action = UserAction(
                user_id=user_id,
                action_type=action_type,
                action_content=enhanced_content,
                template_id=template_id
            )
            
            db.add(user_action)
            db.commit()
            
            logger.info(f"记录功能使用: 用户{user_id}, 功能{feature}, 来源{source}")
            
        except Exception as e:
            logger.error(f"记录功能使用失败: {user_id}, {feature}, {source}, {e}")
    
    def get_user_permission_summary(self, db: Session, user_id: int) -> Dict[str, Any]:
        """获取用户权限汇总"""
        try:
            # 获取卡密权益
            user_benefits = CardService.get_user_benefits(db=db, user_id=user_id)
            
            # 获取会员信息
            membership_info = self.membership_service.get_user_membership_info(db, user_id)
            
            # 获取各功能权限状态
            features = ["resume_export", "idphoto_generate", "premium_templates"]
            feature_permissions = {}
            
            for feature in features:
                permission = self.check_feature_permission(
                    db=db,
                    user_id=user_id,
                    feature=feature,
                    check_usage=True
                )
                feature_permissions[feature] = permission
            
            return {
                "user_id": user_id,
                "membership_info": membership_info,
                "card_benefits": {
                    "total_quota": user_benefits.total_quota,
                    "quota_benefits": [
                        {
                            "id": benefit.id,
                            "remaining_quota": benefit.remaining_quota,
                            "expires_at": benefit.expires_at.isoformat() if benefit.expires_at else None,
                            "source": benefit.source
                        }
                        for benefit in user_benefits.quota_benefits
                    ],
                    "has_active_membership": user_benefits.has_active_membership,
                    "membership_expires_at": user_benefits.membership_expires_at.isoformat() if user_benefits.membership_expires_at else None
                },
                "feature_permissions": feature_permissions,
                "summary": {
                    "primary_benefit_source": (
                        "membership" if user_benefits.has_active_membership
                        else "card_quota" if user_benefits.total_quota > 0
                        else "free_limit"
                    ),
                    "unlimited_access": user_benefits.has_active_membership,
                    "total_available_quota": user_benefits.total_quota
                }
            }
            
        except Exception as e:
            logger.error(f"获取用户权限汇总异常: {user_id}, {e}")
            return {
                "user_id": user_id,
                "error": str(e)
            }


# 创建全局实例
enhanced_permission_service = EnhancedPermissionService()


# 权限检查依赖注入函数
class EnhancedPermissionChecker:
    """增强的权限检查器 - 集成卡密权益"""

    @staticmethod
    def check_resume_export_permission(
        current_user: User = None,
        db: Session = None
    ) -> Dict[str, Any]:
        """检查简历导出权限"""
        if not current_user or not db:
            from fastapi import Depends
            from app.auth.base import get_current_user
            from app.database import get_db
            # 这是为了类型提示，实际使用时会通过依赖注入
            pass

        return enhanced_permission_service.check_feature_permission(
            db=db,
            user_id=current_user.id,
            feature="resume_export",
            quota_needed=1,
            check_usage=True
        )

    @staticmethod
    def check_idphoto_permission(
        current_user: User = None,
        db: Session = None
    ) -> Dict[str, Any]:
        """检查证件照生成权限"""
        return enhanced_permission_service.check_feature_permission(
            db=db,
            user_id=current_user.id,
            feature="idphoto_generate",
            quota_needed=1,
            check_usage=True
        )

    @staticmethod
    def check_premium_template_permission(
        current_user: User = None,
        db: Session = None
    ) -> Dict[str, Any]:
        """检查高级模板权限"""
        return enhanced_permission_service.check_feature_permission(
            db=db,
            user_id=current_user.id,
            feature="premium_templates",
            quota_needed=1,
            check_usage=False
        )
