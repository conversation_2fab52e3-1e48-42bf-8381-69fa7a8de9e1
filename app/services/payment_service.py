"""
支付业务逻辑服务
"""
import uuid
import logging
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List, Tuple
from decimal import Decimal
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_

from app.database import get_db
from app.models import User
from app.models.payment import (
    MembershipPlan, Order, PaymentRecord, UserMembership, PaymentLog,
    OrderStatus, PaymentStatus
)
from app.services.wechat_pay_service import WeChatPayService
from app.services.payment_security import payment_security_service
from app.middleware import get_current_merchant, get_current_merchant_id
from config.wechat_pay_config import get_wechat_pay_config

logger = logging.getLogger(__name__)


class PaymentService:
    """支付业务逻辑服务"""

    def __init__(self):
        self.config = get_wechat_pay_config()

    def _get_wechat_pay_service(self) -> WeChatPayService:
        """获取微信支付服务实例"""
        merchant_config = get_current_merchant()
        if merchant_config:
            return WeChatPayService(merchant_config)
        else:
            # 向后兼容：使用默认配置
            return WeChatPayService()
    
    def generate_order_id(self) -> str:
        """生成订单号"""
        timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
        random_str = str(uuid.uuid4()).replace('-', '')[:8].upper()
        return f"{self.config['order_prefix']}{timestamp}{random_str}"
    
    def get_membership_plans(self, db: Session, active_only: bool = True) -> List[MembershipPlan]:
        """获取会员套餐列表"""
        merchant_id = get_current_merchant_id()

        query = db.query(MembershipPlan)
        if merchant_id:
            query = query.filter(MembershipPlan.merchant_id == merchant_id)
        if active_only:
            query = query.filter(MembershipPlan.is_active == True)

        return query.order_by(MembershipPlan.sort_order).all()

    def get_membership_plan(self, db: Session, plan_id: int) -> Optional[MembershipPlan]:
        """获取指定会员套餐"""
        merchant_id = get_current_merchant_id()

        query = db.query(MembershipPlan).filter(
            MembershipPlan.id == plan_id,
            MembershipPlan.is_active == True
        )
        if merchant_id:
            query = query.filter(MembershipPlan.merchant_id == merchant_id)

        return query.first()
    
    def create_order(
        self,
        db: Session,
        user_id: int,
        plan_id: int,
        client_ip: Optional[str] = None,
        user_agent: Optional[str] = None
    ) -> Order:
        """创建订单"""
        try:
            # 安全验证
            is_valid, error_msg = payment_security_service.validate_order_creation(
                db, user_id, client_ip
            )
            if not is_valid:
                # 记录安全事件
                payment_security_service.log_security_event(
                    db, "order_creation_blocked", user_id, None, client_ip,
                    {"reason": error_msg, "plan_id": plan_id}
                )
                raise ValueError(error_msg)

            # 检查套餐是否存在
            plan = self.get_membership_plan(db, plan_id)
            if not plan:
                raise ValueError(f"套餐不存在或已下架: {plan_id}")

            # 检查用户是否存在且属于当前商户
            merchant_id = get_current_merchant_id()
            user_query = db.query(User).filter(User.id == user_id)
            if merchant_id:
                user_query = user_query.filter(User.merchant_id == merchant_id)

            user = user_query.first()
            if not user:
                raise ValueError(f"用户不存在或不属于当前商户: {user_id}")
            
            # 生成订单号
            order_id = self.generate_order_id()
            
            # 计算订单过期时间
            expired_at = datetime.now() + timedelta(minutes=self.config["order_expire_minutes"])
            
            # 创建订单
            order = Order(
                id=order_id,
                merchant_id=merchant_id,
                user_id=user_id,
                plan_id=plan_id,
                amount=plan.price,
                original_amount=plan.original_price or plan.price,
                discount_amount=(plan.original_price or plan.price) - plan.price,
                status=OrderStatus.PENDING,
                client_ip=client_ip,
                user_agent=user_agent,
                expired_at=expired_at
            )
            
            db.add(order)
            db.commit()
            db.refresh(order)
            
            # 记录日志
            self._log_payment_action(
                db, order_id, user_id, "create_order", "success",
                {"plan_id": plan_id, "amount": float(plan.price)}
            )
            
            logger.info(f"订单创建成功: {order_id}, 用户: {user_id}, 套餐: {plan_id}")
            return order
            
        except Exception as e:
            db.rollback()
            logger.error(f"创建订单失败: {e}")
            raise
    
    def get_user_orders(
        self,
        db: Session,
        user_id: int,
        status: Optional[OrderStatus] = None,
        limit: int = 20,
        offset: int = 0
    ) -> Tuple[List[Order], int]:
        """获取用户订单列表"""
        merchant_id = get_current_merchant_id()

        query = db.query(Order).filter(Order.user_id == user_id)
        if merchant_id:
            query = query.filter(Order.merchant_id == merchant_id)

        if status:
            query = query.filter(Order.status == status)

        total = query.count()
        orders = query.order_by(Order.created_at.desc()).offset(offset).limit(limit).all()

        return orders, total
    
    def get_order(self, db: Session, order_id: str, user_id: Optional[int] = None) -> Optional[Order]:
        """获取订单详情"""
        merchant_id = get_current_merchant_id()

        query = db.query(Order).filter(Order.id == order_id)
        if merchant_id:
            query = query.filter(Order.merchant_id == merchant_id)
        if user_id:
            query = query.filter(Order.user_id == user_id)

        return query.first()
    
    def cancel_order(self, db: Session, order_id: str, user_id: int, reason: Optional[str] = None) -> bool:
        """取消订单"""
        try:
            order = self.get_order(db, order_id, user_id)
            if not order:
                raise ValueError(f"订单不存在: {order_id}")
            
            if order.status != OrderStatus.PENDING:
                raise ValueError(f"订单状态不允许取消: {order.status}")
            
            # 如果有微信预支付ID，尝试关闭微信订单
            if order.wx_prepay_id:
                try:
                    wechat_pay_service = self._get_wechat_pay_service()
                    wechat_pay_service.close_order(order_id)
                except Exception as e:
                    logger.warning(f"关闭微信订单失败: {order_id}, 错误: {e}")
            
            # 更新订单状态
            order.status = OrderStatus.CANCELLED
            order.cancelled_at = datetime.now()
            order.cancel_reason = reason
            
            db.commit()
            
            # 记录日志
            self._log_payment_action(
                db, order_id, user_id, "cancel_order", "success",
                {"reason": reason}
            )
            
            logger.info(f"订单取消成功: {order_id}")
            return True
            
        except Exception as e:
            db.rollback()
            logger.error(f"取消订单失败: {order_id}, 错误: {e}")
            raise
    
    def create_wechat_payment(self, db: Session, order_id: str, user_id: int) -> Dict[str, Any]:
        """创建微信支付"""
        try:
            # 获取微信支付服务实例
            wechat_pay_service = self._get_wechat_pay_service()

            # 安全验证
            is_valid, error_msg = payment_security_service.validate_payment_request(
                db, order_id, user_id
            )
            if not is_valid:
                # 记录安全事件
                payment_security_service.log_security_event(
                    db, "payment_request_blocked", user_id, order_id, None,
                    {"reason": error_msg}
                )
                raise ValueError(error_msg)

            # 获取订单
            order = self.get_order(db, order_id, user_id)
            if not order:
                raise ValueError(f"订单不存在: {order_id}")

            if order.status != OrderStatus.PENDING:
                raise ValueError(f"订单状态不允许支付: {order.status}")

            # 检查订单是否过期
            if datetime.now() > order.expired_at:
                order.status = OrderStatus.EXPIRED
                db.commit()
                raise ValueError("订单已过期")
            
            # 获取用户信息
            user = db.query(User).filter(User.id == user_id).first()
            if not user:
                raise ValueError(f"用户不存在: {user_id}")
            
            # 获取套餐信息
            plan = db.query(MembershipPlan).filter(MembershipPlan.id == order.plan_id).first()
            if not plan:
                raise ValueError(f"套餐不存在: {order.plan_id}")
            
            # 调用微信支付统一下单
            description = f"{plan.name} - {plan.duration_days}天"
            wechat_response = wechat_pay_service.create_jsapi_order(
                order_id=order_id,
                amount=int(order.amount * 100),  # 转换为分
                description=description,
                openid=user.openid
            )
            
            # 更新订单的微信预支付ID
            order.wx_prepay_id = wechat_response.get("prepay_id")
            db.commit()
            
            # 生成小程序支付参数
            pay_params = wechat_pay_service.generate_jsapi_pay_params(order.wx_prepay_id)
            pay_params["order_id"] = order_id
            
            # 记录日志
            self._log_payment_action(
                db, order_id, user_id, "create_wechat_payment", "success",
                {"prepay_id": order.wx_prepay_id}
            )
            
            logger.info(f"微信支付创建成功: {order_id}")
            return pay_params
            
        except Exception as e:
            logger.error(f"创建微信支付失败: {order_id}, 错误: {e}")
            self._log_payment_action(
                db, order_id, user_id, "create_wechat_payment", "failed",
                error_message=str(e)
            )
            raise
    
    def process_payment_callback(self, db: Session, callback_data: Dict[str, Any]) -> bool:
        """处理支付回调"""
        try:
            order_id = callback_data.get("out_trade_no")
            transaction_id = callback_data.get("transaction_id")
            trade_state = callback_data.get("trade_state")

            if not order_id:
                raise ValueError("回调数据缺少订单号")

            # 检测重复支付
            if payment_security_service.detect_duplicate_payment(db, callback_data):
                logger.warning(f"检测到重复支付回调: {order_id}, 交易号: {transaction_id}")
                return True  # 重复回调视为成功处理

            # 获取订单
            order = self.get_order(db, order_id)
            if not order:
                raise ValueError(f"订单不存在: {order_id}")

            # 记录支付回调日志
            self._log_payment_action(
                db, order_id, order.user_id, "payment_callback", "received",
                callback_data
            )
            
            if trade_state == "SUCCESS":
                # 支付成功
                return self._handle_payment_success(db, order, callback_data)
            elif trade_state in ["REFUND", "CLOSED", "REVOKED", "PAYERROR"]:
                # 支付失败或关闭
                return self._handle_payment_failed(db, order, callback_data)
            else:
                logger.warning(f"未知的支付状态: {trade_state}, 订单: {order_id}")
                return False
                
        except Exception as e:
            logger.error(f"处理支付回调失败: {e}")
            return False
    
    def _handle_payment_success(self, db: Session, order: Order, callback_data: Dict[str, Any]) -> bool:
        """处理支付成功"""
        try:
            # 检查订单状态
            if order.status == OrderStatus.PAID:
                logger.info(f"订单已处理过: {order.id}")
                return True
            
            if order.status != OrderStatus.PENDING:
                logger.warning(f"订单状态异常: {order.id}, 状态: {order.status}")
                return False
            
            # 更新订单状态
            order.status = OrderStatus.PAID
            order.wx_transaction_id = callback_data.get("transaction_id")
            order.paid_at = datetime.now()
            
            # 创建支付记录
            payment_record = PaymentRecord(
                order_id=order.id,
                wx_transaction_id=callback_data.get("transaction_id"),
                payment_method="wechat",
                amount=order.amount,
                status=PaymentStatus.SUCCESS,
                callback_data=callback_data
            )
            db.add(payment_record)
            
            # 激活会员
            self._activate_membership(db, order)
            
            db.commit()
            
            # 记录日志
            self._log_payment_action(
                db, order.id, order.user_id, "payment_success", "success",
                {"transaction_id": callback_data.get("transaction_id")}
            )
            
            logger.info(f"支付成功处理完成: {order.id}")
            return True
            
        except Exception as e:
            db.rollback()
            logger.error(f"处理支付成功失败: {order.id}, 错误: {e}")
            return False
    
    def _handle_payment_failed(self, db: Session, order: Order, callback_data: Dict[str, Any]) -> bool:
        """处理支付失败"""
        try:
            # 更新订单状态
            if order.status == OrderStatus.PENDING:
                order.status = OrderStatus.CANCELLED
                order.cancelled_at = datetime.now()
                order.cancel_reason = f"支付失败: {callback_data.get('trade_state_desc', '')}"
            
            # 创建支付记录
            payment_record = PaymentRecord(
                order_id=order.id,
                wx_transaction_id=callback_data.get("transaction_id"),
                payment_method="wechat",
                amount=order.amount,
                status=PaymentStatus.FAILED,
                callback_data=callback_data
            )
            db.add(payment_record)
            
            db.commit()
            
            # 记录日志
            self._log_payment_action(
                db, order.id, order.user_id, "payment_failed", "success",
                callback_data
            )
            
            logger.info(f"支付失败处理完成: {order.id}")
            return True
            
        except Exception as e:
            db.rollback()
            logger.error(f"处理支付失败失败: {order.id}, 错误: {e}")
            return False
    
    def _activate_membership(self, db: Session, order: Order):
        """激活会员"""
        try:
            # 获取套餐信息
            plan = db.query(MembershipPlan).filter(MembershipPlan.id == order.plan_id).first()
            if not plan:
                raise ValueError(f"套餐不存在: {order.plan_id}")
            
            # 获取用户当前会员状态
            current_membership = db.query(UserMembership).filter(
                UserMembership.user_id == order.user_id,
                UserMembership.is_active == True,
                UserMembership.end_date > datetime.now()
            ).first()
            
            # 计算会员开始和结束时间
            if current_membership:
                # 如果已有有效会员，从当前会员结束时间开始
                start_date = current_membership.end_date
                # 将当前会员设为非活跃
                current_membership.is_active = False
                current_membership.deactivated_at = datetime.now()
            else:
                # 如果没有有效会员，从现在开始
                start_date = datetime.now()
            
            end_date = start_date + timedelta(days=plan.duration_days)
            
            # 创建新的会员记录
            membership = UserMembership(
                user_id=order.user_id,
                order_id=order.id,
                plan_id=order.plan_id,
                start_date=start_date,
                end_date=end_date,
                is_active=True
            )
            db.add(membership)
            
            # 更新用户的会员状态
            user = db.query(User).filter(User.id == order.user_id).first()
            if user:
                user.is_member = True
            
            logger.info(f"会员激活成功: 用户{order.user_id}, 套餐{order.plan_id}, 有效期至{end_date}")
            
        except Exception as e:
            logger.error(f"激活会员失败: {order.id}, 错误: {e}")
            raise
    
    def _log_payment_action(
        self, 
        db: Session, 
        order_id: Optional[str], 
        user_id: Optional[int], 
        action: str, 
        status: str,
        request_data: Optional[Dict] = None,
        response_data: Optional[Dict] = None,
        error_message: Optional[str] = None,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None
    ):
        """记录支付操作日志"""
        try:
            log = PaymentLog(
                order_id=order_id,
                user_id=user_id,
                action=action,
                status=status,
                request_data=request_data,
                response_data=response_data,
                error_message=error_message,
                ip_address=ip_address,
                user_agent=user_agent
            )
            db.add(log)
            db.commit()
        except Exception as e:
            logger.error(f"记录支付日志失败: {e}")


# 创建全局实例
payment_service = PaymentService()
