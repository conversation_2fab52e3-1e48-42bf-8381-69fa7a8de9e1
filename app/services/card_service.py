"""
卡密系统核心业务逻辑服务
"""
import secrets
import string
from datetime import datetime, timedelta
from typing import List, Optional, Tuple
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, func

from app.models.card import CardBatch, Card, CardUsageRecord, UserBenefit, BenefitType, CardStatus, BatchStatus
from app.models.user import User
from app.models.payment import UserMembership
from app.schemas.card import (
    CardBatchCreate, CardRedeemRequest, BenefitTypeEnum, 
    CardBatchInfo, CardInfo, UserBenefitSummary
)


class CardService:
    """卡密服务类"""
    
    @staticmethod
    def generate_card_code(length: int = 16) -> str:
        """生成卡密码"""
        # 使用大写字母和数字，排除容易混淆的字符
        chars = string.ascii_uppercase + string.digits
        chars = chars.replace('0', '').replace('O', '').replace('1', '').replace('I', '')
        return ''.join(secrets.choice(chars) for _ in range(length))
    
    @staticmethod
    def generate_batch_code() -> str:
        """生成批次编码"""
        timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
        random_suffix = ''.join(secrets.choice(string.ascii_uppercase + string.digits) for _ in range(4))
        return f"BATCH_{timestamp}_{random_suffix}"
    
    @classmethod
    def create_card_batch(
        cls, 
        db: Session, 
        batch_data: CardBatchCreate, 
        merchant_id: Optional[int] = None,
        created_by: Optional[int] = None
    ) -> Tuple[CardBatch, List[str]]:
        """创建卡密批次并生成卡密"""
        
        # 创建批次
        batch_code = cls.generate_batch_code()
        batch = CardBatch(
            merchant_id=merchant_id,
            batch_name=batch_data.batch_name,
            batch_code=batch_code,
            benefit_type=batch_data.benefit_type.value,
            benefit_value=batch_data.benefit_value,
            benefit_unit=batch_data.benefit_unit.value,
            benefit_description=batch_data.benefit_description,
            total_cards=batch_data.card_count,
            expires_at=batch_data.expires_at,
            created_by=created_by,
            notes=batch_data.notes
        )
        
        db.add(batch)
        db.flush()  # 获取批次ID
        
        # 生成卡密
        card_codes = []
        cards = []
        
        for _ in range(batch_data.card_count):
            # 确保卡密码唯一
            while True:
                card_code = cls.generate_card_code()
                existing = db.query(Card).filter(Card.card_code == card_code).first()
                if not existing:
                    break
            
            card = Card(
                batch_id=batch.id,
                card_code=card_code,
                expires_at=batch_data.expires_at
            )
            cards.append(card)
            card_codes.append(card_code)
        
        db.add_all(cards)
        db.commit()
        
        return batch, card_codes
    
    @classmethod
    def validate_card(cls, db: Session, card_code: str) -> Tuple[bool, str, Optional[Card]]:
        """验证卡密有效性"""
        card = db.query(Card).filter(Card.card_code == card_code.upper()).first()
        
        if not card:
            return False, "卡密不存在", None
        
        if card.status != CardStatus.UNUSED.value:
            return False, "卡密已被使用或已失效", None
        
        # 检查卡密是否过期
        if card.expires_at and card.expires_at < datetime.now():
            # 更新卡密状态为过期
            card.status = CardStatus.EXPIRED.value
            db.commit()
            return False, "卡密已过期", None
        
        # 检查批次状态
        batch = db.query(CardBatch).filter(CardBatch.id == card.batch_id).first()
        if not batch or batch.status != BatchStatus.ACTIVE.value:
            return False, "卡密批次已禁用", None
        
        # 检查批次是否过期
        if batch.expires_at and batch.expires_at < datetime.now():
            return False, "卡密批次已过期", None
        
        return True, "卡密有效", card
    
    @classmethod
    def redeem_card(
        cls, 
        db: Session, 
        user: User, 
        redeem_request: CardRedeemRequest,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None
    ) -> Tuple[bool, str, Optional[dict]]:
        """兑换卡密"""
        
        # 验证卡密
        is_valid, message, card = cls.validate_card(db, redeem_request.card_code)
        if not is_valid:
            return False, message, None
        
        # 获取批次信息
        batch = db.query(CardBatch).filter(CardBatch.id == card.batch_id).first()
        
        try:
            # 更新卡密状态
            card.status = CardStatus.USED.value
            card.used_by = user.id
            card.used_at = datetime.now()
            card.used_ip = ip_address
            
            # 创建使用记录
            usage_record = CardUsageRecord(
                card_id=card.id,
                user_id=user.id,
                benefit_type=batch.benefit_type,
                benefit_value=batch.benefit_value,
                benefit_unit=batch.benefit_unit,
                ip_address=ip_address,
                user_agent=user_agent
            )
            db.add(usage_record)
            
            # 分配权益
            benefit_info = cls._allocate_benefit(db, user, batch)
            
            # 更新批次统计
            batch.used_cards += 1
            
            db.commit()
            
            return True, "卡密兑换成功", {
                "benefit_type": batch.benefit_type,
                "benefit_value": batch.benefit_value,
                "benefit_unit": batch.benefit_unit,
                "benefit_description": batch.benefit_description,
                **benefit_info
            }
            
        except Exception as e:
            db.rollback()
            return False, f"兑换失败: {str(e)}", None
    
    @classmethod
    def _allocate_benefit(cls, db: Session, user: User, batch: CardBatch) -> dict:
        """分配权益给用户"""
        
        if batch.benefit_type == BenefitType.QUOTA.value:
            # 次数权益
            expires_at = None
            if batch.benefit_unit == "days":
                expires_at = datetime.now() + timedelta(days=batch.benefit_value)
            elif batch.benefit_unit == "months":
                expires_at = datetime.now() + timedelta(days=batch.benefit_value * 30)
            
            # 查找是否已有相同类型的权益
            existing_benefit = db.query(UserBenefit).filter(
                and_(
                    UserBenefit.user_id == user.id,
                    UserBenefit.benefit_type == batch.benefit_type,
                    UserBenefit.is_active == True,
                    or_(
                        UserBenefit.expires_at.is_(None),
                        UserBenefit.expires_at > datetime.now()
                    )
                )
            ).first()
            
            if existing_benefit:
                # 累加次数
                existing_benefit.remaining_quota += batch.benefit_value
                existing_benefit.updated_at = datetime.now()
                return {"added_quota": batch.benefit_value}
            else:
                # 创建新的权益记录
                user_benefit = UserBenefit(
                    user_id=user.id,
                    benefit_type=batch.benefit_type,
                    remaining_quota=batch.benefit_value,
                    expires_at=expires_at,
                    source="card",
                    source_id=batch.id
                )
                db.add(user_benefit)
                return {"new_quota": batch.benefit_value}
        
        elif batch.benefit_type == BenefitType.MEMBERSHIP.value:
            # 会员时长权益
            # 查找当前有效的会员记录
            current_membership = db.query(UserMembership).filter(
                and_(
                    UserMembership.user_id == user.id,
                    UserMembership.is_active == True,
                    UserMembership.end_date > datetime.now()
                )
            ).order_by(UserMembership.end_date.desc()).first()
            
            # 计算新的会员结束时间
            if current_membership:
                # 在现有会员基础上延长
                new_end_date = current_membership.end_date
            else:
                # 从现在开始计算
                new_end_date = datetime.now()
            
            if batch.benefit_unit == "days":
                new_end_date += timedelta(days=batch.benefit_value)
            elif batch.benefit_unit == "months":
                new_end_date += timedelta(days=batch.benefit_value * 30)
            
            # 创建会员权益记录（用于跟踪卡密兑换的会员时长）
            user_benefit = UserBenefit(
                user_id=user.id,
                benefit_type=batch.benefit_type,
                remaining_quota=batch.benefit_value,  # 这里存储天数或月数
                expires_at=new_end_date,
                source="card",
                source_id=batch.id
            )
            db.add(user_benefit)
            
            # 更新用户会员状态
            user.is_member = True
            
            return {
                "membership_extended": True,
                "new_end_date": new_end_date.isoformat(),
                "extended_days": batch.benefit_value if batch.benefit_unit == "days" else batch.benefit_value * 30
            }
        
        return {}
    
    @classmethod
    def get_user_benefits(cls, db: Session, user_id: int) -> UserBenefitSummary:
        """获取用户权益汇总"""
        
        # 获取次数权益
        quota_benefits = db.query(UserBenefit).filter(
            and_(
                UserBenefit.user_id == user_id,
                UserBenefit.benefit_type == BenefitType.QUOTA.value,
                UserBenefit.is_active == True,
                UserBenefit.remaining_quota > 0,
                or_(
                    UserBenefit.expires_at.is_(None),
                    UserBenefit.expires_at > datetime.now()
                )
            )
        ).all()
        
        total_quota = sum(benefit.remaining_quota for benefit in quota_benefits)
        
        # 获取会员时长权益
        membership_benefits = db.query(UserBenefit).filter(
            and_(
                UserBenefit.user_id == user_id,
                UserBenefit.benefit_type == BenefitType.MEMBERSHIP.value,
                UserBenefit.is_active == True,
                or_(
                    UserBenefit.expires_at.is_(None),
                    UserBenefit.expires_at > datetime.now()
                )
            )
        ).all()
        
        # 检查是否有有效会员
        has_active_membership = False
        membership_expires_at = None
        
        current_membership = db.query(UserMembership).filter(
            and_(
                UserMembership.user_id == user_id,
                UserMembership.is_active == True,
                UserMembership.end_date > datetime.now()
            )
        ).order_by(UserMembership.end_date.desc()).first()
        
        if current_membership:
            has_active_membership = True
            membership_expires_at = current_membership.end_date
        
        return UserBenefitSummary(
            quota_benefits=quota_benefits,
            total_quota=total_quota,
            membership_benefits=membership_benefits,
            has_active_membership=has_active_membership,
            membership_expires_at=membership_expires_at
        )
    
    @classmethod
    def consume_quota_benefit(cls, db: Session, user_id: int, feature_name: str, quota_needed: int = 1) -> bool:
        """消费用户的次数权益"""
        
        # 按过期时间排序，优先使用即将过期的权益
        benefits = db.query(UserBenefit).filter(
            and_(
                UserBenefit.user_id == user_id,
                UserBenefit.benefit_type == BenefitType.QUOTA.value,
                UserBenefit.is_active == True,
                UserBenefit.remaining_quota >= quota_needed,
                or_(
                    UserBenefit.expires_at.is_(None),
                    UserBenefit.expires_at > datetime.now()
                )
            )
        ).order_by(UserBenefit.expires_at.asc().nullslast()).all()
        
        if not benefits:
            return False
        
        # 使用第一个可用的权益
        benefit = benefits[0]
        benefit.remaining_quota -= quota_needed
        benefit.updated_at = datetime.now()
        
        # 如果权益用完，标记为非活跃
        if benefit.remaining_quota <= 0:
            benefit.is_active = False
        
        db.commit()
        return True

    @classmethod
    def get_card_batch_list(
        cls,
        db: Session,
        merchant_id: Optional[int] = None,
        status: Optional[str] = None,
        benefit_type: Optional[str] = None,
        page: int = 1,
        page_size: int = 20
    ) -> Tuple[List[CardBatch], int]:
        """获取卡密批次列表"""

        query = db.query(CardBatch)

        if merchant_id:
            query = query.filter(CardBatch.merchant_id == merchant_id)

        if status:
            query = query.filter(CardBatch.status == status)

        if benefit_type:
            query = query.filter(CardBatch.benefit_type == benefit_type)

        total = query.count()

        batches = query.order_by(CardBatch.created_at.desc()).offset(
            (page - 1) * page_size
        ).limit(page_size).all()

        return batches, total

    @classmethod
    def get_card_list(
        cls,
        db: Session,
        batch_id: Optional[int] = None,
        status: Optional[str] = None,
        page: int = 1,
        page_size: int = 20
    ) -> Tuple[List[Card], int]:
        """获取卡密列表"""

        query = db.query(Card)

        if batch_id:
            query = query.filter(Card.batch_id == batch_id)

        if status:
            query = query.filter(Card.status == status)

        total = query.count()

        cards = query.order_by(Card.created_at.desc()).offset(
            (page - 1) * page_size
        ).limit(page_size).all()

        return cards, total

    @classmethod
    def get_card_statistics(cls, db: Session, merchant_id: Optional[int] = None) -> dict:
        """获取卡密统计信息"""

        batch_query = db.query(CardBatch)
        card_query = db.query(Card)

        if merchant_id:
            batch_query = batch_query.filter(CardBatch.merchant_id == merchant_id)
            card_query = card_query.join(CardBatch).filter(CardBatch.merchant_id == merchant_id)

        # 批次统计
        total_batches = batch_query.count()

        # 卡密统计
        total_cards = card_query.count()
        used_cards = card_query.filter(Card.status == CardStatus.USED.value).count()
        unused_cards = total_cards - used_cards

        overall_usage_rate = (used_cards / total_cards * 100) if total_cards > 0 else 0

        # 最近批次统计
        recent_batches = batch_query.order_by(CardBatch.created_at.desc()).limit(10).all()
        recent_batch_stats = []

        for batch in recent_batches:
            unused_count = batch.total_cards - batch.used_cards
            usage_rate = (batch.used_cards / batch.total_cards * 100) if batch.total_cards > 0 else 0

            recent_batch_stats.append({
                "id": batch.id,
                "batch_name": batch.batch_name,
                "batch_code": batch.batch_code,
                "benefit_type": batch.benefit_type,
                "benefit_value": batch.benefit_value,
                "benefit_unit": batch.benefit_unit,
                "total_cards": batch.total_cards,
                "used_cards": batch.used_cards,
                "unused_cards": unused_count,
                "usage_rate": round(usage_rate, 2),
                "status": batch.status,
                "created_at": batch.created_at
            })

        return {
            "total_batches": total_batches,
            "total_cards": total_cards,
            "used_cards": used_cards,
            "unused_cards": unused_cards,
            "overall_usage_rate": round(overall_usage_rate, 2),
            "recent_batches": recent_batch_stats
        }

    @classmethod
    def disable_card_batch(cls, db: Session, batch_id: int) -> bool:
        """禁用卡密批次"""

        batch = db.query(CardBatch).filter(CardBatch.id == batch_id).first()
        if not batch:
            return False

        batch.status = BatchStatus.DISABLED.value
        batch.updated_at = datetime.now()

        # 同时禁用该批次下所有未使用的卡密
        db.query(Card).filter(
            and_(
                Card.batch_id == batch_id,
                Card.status == CardStatus.UNUSED.value
            )
        ).update({"status": CardStatus.DISABLED.value})

        db.commit()
        return True

    @classmethod
    def get_user_card_usage_history(
        cls,
        db: Session,
        user_id: int,
        page: int = 1,
        page_size: int = 20
    ) -> Tuple[List[CardUsageRecord], int]:
        """获取用户卡密使用历史"""

        query = db.query(CardUsageRecord).filter(CardUsageRecord.user_id == user_id)

        total = query.count()

        records = query.order_by(CardUsageRecord.used_at.desc()).offset(
            (page - 1) * page_size
        ).limit(page_size).all()

        return records, total
