"""
卡密管理API接口 - 管理员专用
"""
from fastapi import APIRouter, Depends, HTTPException, status, Request
from sqlalchemy.orm import Session
from typing import Optional
import math

from app.database import get_db
from app.auth.base import get_current_user
from app.auth.admin_auth import require_admin
from app.models.user import User
from app.services.card_service import CardService
from app.schemas.card import (
    CardBatchCreate, CardBatchInfo, CardBatchListRequest, CardBatchListResponse,
    CardListRequest, CardListResponse, CardBatchUpdateRequest,
    CardGenerateResponse, CardStatisticsResponse, BatchStatusEnum
)

router = APIRouter(prefix="/admin/cards", tags=["卡密管理"])


@router.post("/batches", response_model=CardGenerateResponse)
async def create_card_batch(
    batch_data: CardBatchCreate,
    request: Request,
    current_user: User = Depends(require_admin),
    db: Session = Depends(get_db)
):
    """
    创建卡密批次并生成卡密
    """
    try:
        # 获取商户ID（从中间件获取）
        merchant_id = getattr(request.state, 'merchant_id', None)
        
        # 创建批次并生成卡密
        batch, card_codes = CardService.create_card_batch(
            db=db,
            batch_data=batch_data,
            merchant_id=merchant_id,
            created_by=current_user.id
        )
        
        return CardGenerateResponse(
            batch_id=batch.id,
            batch_code=batch.batch_code,
            generated_count=len(card_codes),
            card_codes=card_codes
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建卡密批次失败: {str(e)}"
        )


@router.get("/batches", response_model=CardBatchListResponse)
async def get_card_batches(
    request: Request,
    page: int = 1,
    page_size: int = 20,
    status: Optional[BatchStatusEnum] = None,
    benefit_type: Optional[str] = None,
    current_user: User = Depends(require_admin),
    db: Session = Depends(get_db)
):
    """
    获取卡密批次列表
    """
    try:
        # 获取商户ID
        merchant_id = getattr(request.state, 'merchant_id', None)
        
        # 参数验证
        if page < 1:
            page = 1
        if page_size < 1 or page_size > 100:
            page_size = 20
        
        # 获取批次列表
        batches, total = CardService.get_card_batch_list(
            db=db,
            merchant_id=merchant_id,
            status=status.value if status else None,
            benefit_type=benefit_type,
            page=page,
            page_size=page_size
        )
        
        total_pages = math.ceil(total / page_size) if total > 0 else 1
        
        return CardBatchListResponse(
            items=[CardBatchInfo.from_orm(batch) for batch in batches],
            total=total,
            page=page,
            page_size=page_size,
            total_pages=total_pages
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取批次列表失败: {str(e)}"
        )


@router.get("/batches/{batch_id}", response_model=CardBatchInfo)
async def get_card_batch(
    batch_id: int,
    current_user: User = Depends(require_admin),
    db: Session = Depends(get_db)
):
    """
    获取卡密批次详情
    """
    from app.models.card import CardBatch
    
    batch = db.query(CardBatch).filter(CardBatch.id == batch_id).first()
    if not batch:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="卡密批次不存在"
        )
    
    return CardBatchInfo.from_orm(batch)


@router.put("/batches/{batch_id}")
async def update_card_batch(
    batch_id: int,
    update_data: CardBatchUpdateRequest,
    current_user: User = Depends(require_admin),
    db: Session = Depends(get_db)
):
    """
    更新卡密批次
    """
    from app.models.card import CardBatch
    
    batch = db.query(CardBatch).filter(CardBatch.id == batch_id).first()
    if not batch:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="卡密批次不存在"
        )
    
    try:
        # 更新字段
        if update_data.status is not None:
            batch.status = update_data.status.value
        
        if update_data.notes is not None:
            batch.notes = update_data.notes
        
        db.commit()
        
        return {"message": "批次更新成功"}
        
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新批次失败: {str(e)}"
        )


@router.post("/batches/{batch_id}/disable")
async def disable_card_batch(
    batch_id: int,
    current_user: User = Depends(require_admin),
    db: Session = Depends(get_db)
):
    """
    禁用卡密批次
    """
    try:
        success = CardService.disable_card_batch(db=db, batch_id=batch_id)
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="卡密批次不存在"
            )
        
        return {"message": "批次禁用成功"}
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"禁用批次失败: {str(e)}"
        )


@router.get("/cards", response_model=CardListResponse)
async def get_cards(
    batch_id: Optional[int] = None,
    status: Optional[str] = None,
    page: int = 1,
    page_size: int = 20,
    current_user: User = Depends(require_admin),
    db: Session = Depends(get_db)
):
    """
    获取卡密列表
    """
    try:
        # 参数验证
        if page < 1:
            page = 1
        if page_size < 1 or page_size > 100:
            page_size = 20
        
        # 获取卡密列表
        cards, total = CardService.get_card_list(
            db=db,
            batch_id=batch_id,
            status=status,
            page=page,
            page_size=page_size
        )
        
        total_pages = math.ceil(total / page_size) if total > 0 else 1
        
        return CardListResponse(
            items=[{
                "id": card.id,
                "batch_id": card.batch_id,
                "card_code": card.card_code,
                "status": card.status,
                "used_by": card.used_by,
                "used_at": card.used_at,
                "used_ip": card.used_ip,
                "expires_at": card.expires_at,
                "created_at": card.created_at
            } for card in cards],
            total=total,
            page=page,
            page_size=page_size,
            total_pages=total_pages
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取卡密列表失败: {str(e)}"
        )


@router.get("/statistics", response_model=CardStatisticsResponse)
async def get_card_statistics(
    request: Request,
    current_user: User = Depends(require_admin),
    db: Session = Depends(get_db)
):
    """
    获取卡密统计信息
    """
    try:
        # 获取商户ID
        merchant_id = getattr(request.state, 'merchant_id', None)
        
        # 获取统计信息
        stats = CardService.get_card_statistics(db=db, merchant_id=merchant_id)
        
        return CardStatisticsResponse(**stats)
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取统计信息失败: {str(e)}"
        )
