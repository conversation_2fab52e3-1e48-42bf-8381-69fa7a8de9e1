"""
会员权益消费API路由
专门处理会员权益的消费记录和配额管理
"""

from fastapi import APIRouter, HTTPException, Depends, status, Query, Body
from sqlalchemy.orm import Session
from typing import Optional, Dict, Any
import logging

from app.database import get_db
from app.models import User
from app.auth import get_current_user
from app.services.membership_usage_service import membership_usage_service

logger = logging.getLogger(__name__)

router = APIRouter(
    prefix="/membership/usage",
    tags=["会员权益消费"],
    responses={404: {"description": "未找到"}},
)


@router.post("/check-quota")
async def check_quota_permission(
    feature_name: str = Body(..., description="功能名称"),
    quota_type: str = Body("daily", description="配额类型"),
    requested_quota: int = Body(1, description="请求配额数量"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    检查配额权限
    前端在执行会员功能前调用此接口检查权限
    """
    try:
        result = membership_usage_service.check_quota_permission(
            db=db,
            user_id=current_user.id,
            feature_name=feature_name,
            quota_type=quota_type,
            requested_quota=requested_quota
        )
        
        return {
            "success": True,
            "data": result,
            "message": "配额权限检查完成"
        }
        
    except Exception as e:
        logger.exception("检查配额权限异常")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="检查配额权限失败"
        )


@router.post("/consume")
async def consume_quota(
    feature_name: str = Body(..., description="功能名称"),
    consumed_quota: int = Body(1, description="消耗配额数量"),
    quota_type: str = Body("daily", description="配额类型"),
    related_action_id: Optional[int] = Body(None, description="关联的用户操作ID"),
    resource_info: Optional[Dict[str, Any]] = Body(None, description="资源信息"),
    business_context: Optional[Dict[str, Any]] = Body(None, description="业务上下文"),
    auto_confirm: bool = Body(True, description="是否自动确认"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    消费配额
    前端在用户真正完成操作后调用此接口消费配额
    """
    try:
        result = membership_usage_service.consume_quota(
            db=db,
            user_id=current_user.id,
            feature_name=feature_name,
            consumed_quota=consumed_quota,
            quota_type=quota_type,
            related_action_id=related_action_id,
            resource_info=resource_info,
            business_context=business_context,
            auto_confirm=auto_confirm
        )
        
        if result["success"]:
            logger.info(f"用户 {current_user.id} 配额消费成功: {feature_name}")
            return result
        else:
            logger.warning(f"用户 {current_user.id} 配额消费失败: {result['message']}")
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=result
            )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.exception("消费配额异常")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="消费配额失败"
        )


@router.get("/quota-status")
async def get_quota_status(
    feature_name: str = Query(..., description="功能名称"),
    quota_type: str = Query("daily", description="配额类型"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    获取用户配额状态
    """
    try:
        result = membership_usage_service.check_quota_permission(
            db=db,
            user_id=current_user.id,
            feature_name=feature_name,
            quota_type=quota_type,
            requested_quota=0  # 只查询状态，不检查具体配额
        )
        
        return {
            "success": True,
            "data": {
                "feature_name": feature_name,
                "quota_type": quota_type,
                "quota_info": result["quota_info"],
                "is_member": result.get("is_member", False)
            },
            "message": "获取配额状态成功"
        }
        
    except Exception as e:
        logger.exception("获取配额状态异常")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取配额状态失败"
        )


@router.get("/usage-history")
async def get_usage_history(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
    feature_name: Optional[str] = Query(None, description="功能名称筛选"),
    days: int = Query(30, ge=1, le=365, description="查询天数"),
    limit: int = Query(20, ge=1, le=100, description="返回数量限制"),
    offset: int = Query(0, ge=0, description="偏移量")
):
    """
    获取用户配额使用历史
    """
    try:
        from app.models.membership_usage import MembershipUsage
        from sqlalchemy import and_, desc
        from datetime import datetime, timedelta
        
        # 构建查询条件
        start_date = datetime.now() - timedelta(days=days)
        query = db.query(MembershipUsage).filter(
            and_(
                MembershipUsage.user_id == current_user.id,
                MembershipUsage.usage_date >= start_date
            )
        )
        
        if feature_name:
            query = query.filter(MembershipUsage.feature_name == feature_name)
        
        # 获取总数
        total = query.count()
        
        # 分页查询
        usage_records = query.order_by(desc(MembershipUsage.usage_date)).offset(offset).limit(limit).all()
        
        # 转换为响应格式
        records = []
        for record in usage_records:
            records.append({
                "id": record.id,
                "feature_name": record.feature_name,
                "consumed_quota": record.consumed_quota,
                "quota_type": record.quota_type,
                "usage_status": record.usage_status,
                "usage_date": record.usage_date,
                "resource_info": record.resource_info,
                "business_context": record.business_context
            })
        
        return {
            "success": True,
            "data": {
                "total": total,
                "records": records,
                "pagination": {
                    "limit": limit,
                    "offset": offset,
                    "has_more": offset + limit < total
                }
            },
            "message": "获取使用历史成功"
        }
        
    except Exception as e:
        logger.exception("获取使用历史异常")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取使用历史失败"
        )
