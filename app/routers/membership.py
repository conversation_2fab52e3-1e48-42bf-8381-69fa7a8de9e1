"""
会员管理相关API路由
"""
from fastapi import APIRouter, HTTPException, Depends, status
from sqlalchemy.orm import Session
from datetime import datetime
from typing import List, Optional
import logging

from app.database import get_db
from app.models import User
from app.models.payment import UserMembership
from app.schemas.payment import MembershipStatusResponse, UserMembershipInfo
from app.auth import get_current_user

from app.models.payment import MembershipPlan

logger = logging.getLogger(__name__)

router = APIRouter(
    prefix="/membership",
    tags=["会员管理"],
    responses={404: {"description": "未找到"}},
)


@router.get("/status", response_model=MembershipStatusResponse)
async def get_membership_status(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    获取用户会员状态
    """
    try:
        # 获取当前有效的会员信息
        current_membership = db.query(UserMembership).filter(
            UserMembership.user_id == current_user.id,
            UserMembership.is_active == True,
            UserMembership.end_date > datetime.now()
        ).first()
        
        # 获取会员历史记录
        membership_history = db.query(UserMembership).filter(
            UserMembership.user_id == current_user.id
        ).order_by(UserMembership.created_at.desc()).limit(10).all()
        
        # 构建响应数据
        current_membership_info = None
        if current_membership:
            # 获取套餐信息
            
            plan = db.query(MembershipPlan).filter(
                MembershipPlan.id == current_membership.plan_id
            ).first()
            
            current_membership_info = UserMembershipInfo.model_validate(current_membership)
            current_membership_info.plan_name = plan.name if plan else "未知套餐"
        
        # 构建历史记录
        history_infos = []
        for membership in membership_history:

            plan = db.query(MembershipPlan).filter(
                MembershipPlan.id == membership.plan_id
            ).first()
            
            membership_info = UserMembershipInfo.model_validate(membership)
            membership_info.plan_name = plan.name if plan else "未知套餐"
            history_infos.append(membership_info)
        
        return MembershipStatusResponse(
            is_member=current_membership is not None,
            current_membership=current_membership_info,
            membership_history=history_infos
        )
        
    except Exception as e:
        logger.exception("获取会员状态异常")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取会员状态失败"
        )


@router.get("/benefits")
async def get_membership_benefits(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    获取会员权益信息
    """
    try:
        # 获取当前有效的会员信息
        current_membership = db.query(UserMembership).filter(
            UserMembership.user_id == current_user.id,
            UserMembership.is_active == True,
            UserMembership.end_date > datetime.now()
        ).first()
        
        if not current_membership:
            return {
                "is_member": False,
                "message": "您还不是会员，升级会员享受更多特权",
                "benefits": [
                    "无限制简历导出",
                    "高级模板使用",
                    "证件照生成",
                    "优先客服支持"
                ]
            }
        
        # 获取套餐信息

        plan = db.query(MembershipPlan).filter(
            MembershipPlan.id == current_membership.plan_id
        ).first()
        
        if not plan:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="套餐信息不存在"
            )
        
        # 计算剩余天数
        remaining_days = (current_membership.end_date - datetime.now()).days
        remaining_days = max(0, remaining_days)
        
        return {
            "is_member": True,
            "plan_name": plan.name,
            "end_date": current_membership.end_date,
            "remaining_days": remaining_days,
            "benefits": plan.features or [],
            "message": f"您是{plan.name}用户，还有{remaining_days}天到期"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.exception("获取会员权益异常")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取会员权益失败"
        )


@router.get("/check-permission/{feature}")
async def check_membership_permission(
    feature: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    检查会员权限
    """
    try:
        # 获取当前有效的会员信息
        current_membership = db.query(UserMembership).filter(
            UserMembership.user_id == current_user.id,
            UserMembership.is_active == True,
            UserMembership.end_date > datetime.now()
        ).first()
        
        if not current_membership:
            return {
                "has_permission": False,
                "is_member": False,
                "message": f"需要会员权限才能使用{feature}功能",
                "upgrade_required": True
            }
        
        # 获取套餐信息

        plan = db.query(MembershipPlan).filter(
            MembershipPlan.id == current_membership.plan_id
        ).first()
        
        if not plan:
            return {
                "has_permission": False,
                "is_member": True,
                "message": "套餐信息异常",
                "upgrade_required": False
            }
        
        # 检查功能权限
        features = plan.features or []
        has_permission = feature in features or "无限制简历导出" in features
        
        return {
            "has_permission": has_permission,
            "is_member": True,
            "plan_name": plan.name,
            "message": "权限验证成功" if has_permission else f"{feature}功能需要更高级别的会员权限",
            "upgrade_required": not has_permission
        }
        
    except Exception as e:
        logger.exception("检查会员权限异常")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="检查会员权限失败"
        )


@router.post("/sync-status")
async def sync_membership_status(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    同步会员状态
    """
    try:
        # 检查是否有有效的会员记录
        active_membership = db.query(UserMembership).filter(
            UserMembership.user_id == current_user.id,
            UserMembership.is_active == True,
            UserMembership.end_date > datetime.now()
        ).first()
        
        # 更新用户的会员状态
        current_user.is_member = active_membership is not None
        
        # 如果有过期的会员记录，将其设为非活跃
        expired_memberships = db.query(UserMembership).filter(
            UserMembership.user_id == current_user.id,
            UserMembership.is_active == True,
            UserMembership.end_date <= datetime.now()
        ).all()
        
        for membership in expired_memberships:
            membership.is_active = False
            membership.deactivated_at = datetime.now()
        
        db.commit()
        
        return {
            "success": True,
            "is_member": current_user.is_member,
            "message": "会员状态同步成功",
            "expired_count": len(expired_memberships)
        }
        
    except Exception as e:
        db.rollback()
        logger.exception("同步会员状态异常")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="同步会员状态失败"
        )


@router.get("/usage-stats")
async def get_membership_usage_stats(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    获取会员使用统计
    """
    try:
        # 获取用户行为统计
        from app.models import UserAction
        from sqlalchemy import func, and_
        from datetime import datetime, timedelta
        
        # 最近30天的使用统计
        thirty_days_ago = datetime.now() - timedelta(days=30)
        
        # 简历导出次数
        resume_exports = db.query(func.count(UserAction.id)).filter(
            and_(
                UserAction.user_id == current_user.id,
                UserAction.action_type.in_(["export_pdf", "export_jpeg"]),
                UserAction.created_at >= thirty_days_ago
            )
        ).scalar() or 0
        
        # 证件照生成次数
        idphoto_generates = db.query(func.count(UserAction.id)).filter(
            and_(
                UserAction.user_id == current_user.id,
                UserAction.action_type == "generate_idphoto",
                UserAction.created_at >= thirty_days_ago
            )
        ).scalar() or 0
        
        # 模板使用次数
        template_uses = db.query(func.count(UserAction.id)).filter(
            and_(
                UserAction.user_id == current_user.id,
                UserAction.action_type == "use_template",
                UserAction.created_at >= thirty_days_ago
            )
        ).scalar() or 0
        
        # 获取会员信息
        current_membership = db.query(UserMembership).filter(
            UserMembership.user_id == current_user.id,
            UserMembership.is_active == True,
            UserMembership.end_date > datetime.now()
        ).first()
        
        return {
            "is_member": current_membership is not None,
            "stats_period": "最近30天",
            "usage_stats": {
                "resume_exports": resume_exports,
                "idphoto_generates": idphoto_generates,
                "template_uses": template_uses,
                "total_actions": resume_exports + idphoto_generates + template_uses
            },
            "membership_info": {
                "plan_name": None,
                "end_date": None,
                "remaining_days": 0
            } if not current_membership else {
                "plan_name": "会员套餐",  # 这里可以关联查询套餐名称
                "end_date": current_membership.end_date,
                "remaining_days": max(0, (current_membership.end_date - datetime.now()).days)
            }
        }
        
    except Exception as e:
        logger.exception("获取会员使用统计异常")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取使用统计失败"
        )
