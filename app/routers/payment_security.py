"""
支付安全管理API路由
"""
from fastapi import APIRouter, HTTPException, Depends, status, Request
from sqlalchemy.orm import Session
from typing import Optional
import logging

from app.database import get_db
from app.models import User
from app.services.payment_security import payment_security_service
from app.auth import get_current_user, require_admin

logger = logging.getLogger(__name__)

router = APIRouter(
    prefix="/payment/security",
    tags=["支付安全"],
    responses={404: {"description": "未找到"}},
)


@router.get("/check-order-permission")
async def check_order_creation_permission(
    request: Request,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    检查订单创建权限
    """
    try:
        client_ip = request.client.host if request.client else None
        
        is_valid, error_msg = payment_security_service.validate_order_creation(
            db=db,
            user_id=current_user.id,
            client_ip=client_ip
        )
        
        return {
            "can_create_order": is_valid,
            "message": error_msg,
            "user_id": current_user.id,
            "client_ip": client_ip
        }
        
    except Exception as e:
        logger.exception("检查订单创建权限异常")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="权限检查失败"
        )


@router.get("/check-payment-permission/{order_id}")
async def check_payment_permission(
    order_id: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    检查支付权限
    """
    try:
        is_valid, error_msg = payment_security_service.validate_payment_request(
            db=db,
            order_id=order_id,
            user_id=current_user.id
        )
        
        return {
            "can_pay": is_valid,
            "message": error_msg,
            "order_id": order_id,
            "user_id": current_user.id
        }
        
    except Exception as e:
        logger.exception("检查支付权限异常")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="权限检查失败"
        )


@router.get("/statistics")
async def get_security_statistics(
    hours: int = 24,
    admin_user: User = Depends(require_admin),
    db: Session = Depends(get_db)
):
    """
    获取安全统计信息（仅管理员可访问）
    """
    try:
        
        # 限制查询范围
        hours = min(hours, 168)  # 最多7天
        
        stats = payment_security_service.get_security_statistics(db, hours)
        
        return {
            "success": True,
            "data": stats,
            "message": f"获取最近{hours}小时的安全统计成功"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.exception("获取安全统计异常")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取安全统计失败"
        )


@router.post("/report-suspicious-activity")
async def report_suspicious_activity(
    request: Request,
    activity_type: str,
    description: Optional[str] = None,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    报告可疑活动
    """
    try:
        client_ip = request.client.host if request.client else None
        
        # 记录可疑活动
        payment_security_service.log_security_event(
            db=db,
            event_type=f"suspicious_{activity_type}",
            user_id=current_user.id,
            ip_address=client_ip,
            details={
                "activity_type": activity_type,
                "description": description,
                "reported_by_user": True
            }
        )
        
        return {
            "success": True,
            "message": "可疑活动报告已记录",
            "activity_type": activity_type
        }
        
    except Exception as e:
        logger.exception("报告可疑活动异常")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="报告可疑活动失败"
        )


@router.get("/user-security-info")
async def get_user_security_info(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    获取用户安全信息
    """
    try:
        from app.models.payment import Order, PaymentLog
        from sqlalchemy import func, and_
        from datetime import datetime, timedelta
        
        # 获取用户最近24小时的活动统计
        twenty_four_hours_ago = datetime.now() - timedelta(hours=24)
        
        # 订单统计
        order_stats = db.query(
            Order.status,
            func.count(Order.id).label('count')
        ).filter(
            and_(
                Order.user_id == current_user.id,
                Order.created_at >= twenty_four_hours_ago
            )
        ).group_by(Order.status).all()
        
        # 支付尝试统计
        payment_attempts = db.query(func.count(PaymentLog.id)).filter(
            and_(
                PaymentLog.user_id == current_user.id,
                PaymentLog.action == "create_wechat_payment",
                PaymentLog.created_at >= twenty_four_hours_ago
            )
        ).scalar() or 0
        
        # 安全事件统计
        security_events = db.query(func.count(PaymentLog.id)).filter(
            and_(
                PaymentLog.user_id == current_user.id,
                PaymentLog.action.like('security_%'),
                PaymentLog.created_at >= twenty_four_hours_ago
            )
        ).scalar() or 0
        
        return {
            "user_id": current_user.id,
            "period": "最近24小时",
            "order_statistics": {stat.status.value: stat.count for stat in order_stats},
            "payment_attempts": payment_attempts,
            "security_events": security_events,
            "account_status": "正常" if security_events == 0 else "有安全事件",
            "recommendations": [
                "定期检查订单状态",
                "避免频繁创建订单",
                "如发现异常活动请及时报告"
            ]
        }
        
    except Exception as e:
        logger.exception("获取用户安全信息异常")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取用户安全信息失败"
        )


@router.post("/validate-order-hash")
async def validate_order_hash(
    order_data: dict,
    expected_hash: str,
    current_user: User = Depends(get_current_user)
):
    """
    验证订单哈希值
    """
    try:
        # 生成订单哈希
        calculated_hash = payment_security_service.generate_order_hash(order_data)
        
        is_valid = calculated_hash == expected_hash
        
        return {
            "is_valid": is_valid,
            "calculated_hash": calculated_hash,
            "expected_hash": expected_hash,
            "message": "哈希验证通过" if is_valid else "哈希验证失败，数据可能被篡改"
        }
        
    except Exception as e:
        logger.exception("验证订单哈希异常")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="哈希验证失败"
        )


@router.get("/security-tips")
async def get_security_tips():
    """
    获取安全提示
    """
    return {
        "security_tips": [
            {
                "title": "订单安全",
                "tips": [
                    "不要频繁创建订单",
                    "及时完成或取消未支付订单",
                    "注意订单过期时间"
                ]
            },
            {
                "title": "支付安全", 
                "tips": [
                    "确认订单信息后再支付",
                    "不要重复点击支付按钮",
                    "支付完成后及时查看结果"
                ]
            },
            {
                "title": "账户安全",
                "tips": [
                    "保护好个人信息",
                    "发现异常及时联系客服",
                    "定期检查订单和支付记录"
                ]
            }
        ],
        "emergency_contact": {
            "customer_service": "在线客服",
            "report_issue": "问题反馈功能"
        }
    }
