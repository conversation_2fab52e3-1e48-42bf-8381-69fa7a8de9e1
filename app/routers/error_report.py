"""
错误上报系统API路由 - 简化版本
"""
from fastapi import APIRouter, HTTPException, Depends, status, Query
from sqlalchemy.orm import Session
from sqlalchemy import func, desc
from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta
import logging

from app.database import get_db
from app.models import User, ErrorReport
from app.schemas.user import (
    ErrorReportCreate,
    ErrorReportBatch,
    ErrorReportInfo,
    # ErrorReportResponse,
    ErrorStatsResponse,
    ListResponse,
)
from app.auth import get_current_user, require_admin

logger = logging.getLogger(__name__)

# 新的简化错误上报路由
router = APIRouter(
    prefix="/error",
    tags=["错误上报"],
    responses={404: {"description": "未找到"}},
)


def _extract_user_info_from_context(error_context: Optional[Dict[str, Any]]) -> tuple:
    """从错误上下文中提取用户信息"""
    user_id = None
    openid = None

    if error_context:
        # 从上下文中提取用户信息
        user_info = error_context.get('user_info', {})
        if isinstance(user_info, dict):
            # 注意：user_id应该是整数，如果是字符串则忽略
            raw_user_id = user_info.get('user_id')
            if isinstance(raw_user_id, int):
                user_id = raw_user_id
            elif isinstance(raw_user_id, str) and raw_user_id.isdigit():
                user_id = int(raw_user_id)
            # 如果是非数字字符串，则忽略，只使用openid

            openid = user_info.get('openid')

        # 也可能直接在context根级别
        if not openid:
            openid = error_context.get('openid')
        if not user_id:
            raw_user_id = error_context.get('user_id')
            if isinstance(raw_user_id, int):
                user_id = raw_user_id
            elif isinstance(raw_user_id, str) and raw_user_id.isdigit():
                user_id = int(raw_user_id)

    return user_id, openid


def _extract_enhanced_info_from_context(error_context: Optional[Dict[str, Any]]) -> Dict[str, Any]:
    """从错误上下文中提取增强信息"""
    enhanced_info = {}

    if not error_context:
        return enhanced_info

    # 提取请求相关信息
    request_info = error_context.get('request_info', {})
    if isinstance(request_info, dict):
        enhanced_info['request_url'] = request_info.get('url')
        enhanced_info['request_method'] = request_info.get('method')
        enhanced_info['request_params'] = request_info.get('params')
        enhanced_info['response_status'] = request_info.get('status_code')
        enhanced_info['response_time'] = request_info.get('response_time')

    # 也可能直接在context根级别
    enhanced_info['request_url'] = enhanced_info.get('request_url') or error_context.get('request_url')
    enhanced_info['request_method'] = enhanced_info.get('request_method') or error_context.get('request_method')
    enhanced_info['response_status'] = enhanced_info.get('response_status') or error_context.get('response_status')

    # 提取会话信息
    session_info = error_context.get('session_info', {})
    if isinstance(session_info, dict):
        enhanced_info['session_id'] = session_info.get('session_id')
        enhanced_info['user_ip'] = session_info.get('user_ip')
        enhanced_info['user_location'] = session_info.get('user_location')

    # 也可能直接在context根级别
    enhanced_info['session_id'] = enhanced_info.get('session_id') or error_context.get('session_id')
    enhanced_info['user_ip'] = enhanced_info.get('user_ip') or error_context.get('user_ip')

    # 提取应用状态信息
    app_info = error_context.get('app_info', {})
    if isinstance(app_info, dict):
        enhanced_info['app_state'] = app_info.get('app_state')
        enhanced_info['memory_usage'] = app_info.get('memory_usage')
        enhanced_info['battery_level'] = app_info.get('battery_level')

    # 也可能直接在context根级别
    enhanced_info['memory_usage'] = enhanced_info.get('memory_usage') or error_context.get('memory_usage')
    enhanced_info['battery_level'] = enhanced_info.get('battery_level') or error_context.get('battery_level')

    # 提取时间信息
    enhanced_info['timestamp'] = error_context.get('timestamp')
    if enhanced_info['timestamp'] and isinstance(enhanced_info['timestamp'], str):
        try:
            from datetime import datetime
            enhanced_info['timestamp'] = datetime.fromisoformat(enhanced_info['timestamp'].replace('Z', '+00:00'))
        except:
            enhanced_info['timestamp'] = None

    # 清理None值
    return {k: v for k, v in enhanced_info.items() if v is not None}

@router.post("/report")
async def report_error(
    error_data: ErrorReportCreate,
    db: Session = Depends(get_db)
):
    """
    简化版错误上报接口
    接口路径: POST /error/report
    请求格式: { error_type: str, error_message: str, error_context: dict }
    不需要认证，避免认证失败导致无法上报错误
    """
    try:
        # 从错误上下文中提取用户信息
        extracted_user_id, openid = _extract_user_info_from_context(error_data.error_context)

        # 从错误上下文中提取增强信息
        enhanced_info = _extract_enhanced_info_from_context(error_data.error_context)

        # 验证用户ID是否真实存在
        final_user_id = None
        if extracted_user_id:
            # 检查用户是否存在
            user = db.query(User).filter(User.id == extracted_user_id).first()
            if user:
                final_user_id = extracted_user_id

        # 如果没有有效的user_id，尝试根据openid查找
        if not final_user_id and openid:
            user = db.query(User).filter(User.openid == openid).first()
            if user:
                final_user_id = user.id

        # 创建错误记录
        current_time = datetime.now()
        error_report = ErrorReport(
            user_id=final_user_id,  # 只有存在的用户ID才会被设置
            openid=openid,
            error_type=error_data.error_type,
            error_message=error_data.error_message,
            error_context=error_data.error_context,

            # 时间字段
            timestamp=enhanced_info.get('timestamp', current_time),
            reported_at=current_time,

            # 请求相关信息
            request_url=enhanced_info.get('request_url'),
            request_method=enhanced_info.get('request_method'),
            request_params=enhanced_info.get('request_params'),
            response_status=enhanced_info.get('response_status'),
            response_time=enhanced_info.get('response_time'),

            # 用户会话信息
            session_id=enhanced_info.get('session_id'),
            user_ip=enhanced_info.get('user_ip'),
            user_location=enhanced_info.get('user_location'),

            # 应用状态信息
            app_state=enhanced_info.get('app_state'),
            memory_usage=enhanced_info.get('memory_usage'),
            battery_level=enhanced_info.get('battery_level')
        )

        db.add(error_report)
        db.commit()
        db.refresh(error_report)

        logger.info(f"错误上报成功: {error_report.id}, 类型: {error_data.error_type}")

        # 返回客户端期望的格式
        return {
            "success": True,
            "message": "错误上报成功",
            "data": {
                "report_id": f"error_{error_report.id}",
                "timestamp": datetime.now().isoformat()
            }
        }

    except Exception:
        logger.exception("错误上报异常")
        db.rollback()
        # 错误上报失败不应该抛出异常，避免影响用户体验
        return {
            "success": False,
            "message": "错误上报失败，但不影响正常使用",
            "data": None
        }

@router.post("/report/batch")
async def report_error_batch(
    batch_data: ErrorReportBatch,
    db: Session = Depends(get_db)
):
    """
    简化版批量错误上报接口
    接口路径: POST /error/report/batch
    请求格式: { errors: [{ error_type: str, error_message: str, error_context: dict }] }
    不需要认证，避免认证失败导致无法上报错误
    """
    try:
        error_reports = []

        current_time = datetime.now()
        for error_data in batch_data.errors:
            # 从错误上下文中提取用户信息
            extracted_user_id, openid = _extract_user_info_from_context(error_data.error_context)

            # 从错误上下文中提取增强信息
            enhanced_info = _extract_enhanced_info_from_context(error_data.error_context)

            # 验证用户ID是否真实存在
            final_user_id = None
            if extracted_user_id:
                # 检查用户是否存在
                user = db.query(User).filter(User.id == extracted_user_id).first()
                if user:
                    final_user_id = extracted_user_id

            # 如果没有有效的user_id，尝试根据openid查找
            if not final_user_id and openid:
                user = db.query(User).filter(User.openid == openid).first()
                if user:
                    final_user_id = user.id

            # 创建错误记录
            error_report = ErrorReport(
                user_id=final_user_id,  # 只有存在的用户ID才会被设置
                openid=openid,
                error_type=error_data.error_type,
                error_message=error_data.error_message,
                error_context=error_data.error_context,

                # 时间字段
                timestamp=enhanced_info.get('timestamp', current_time),
                reported_at=current_time,

                # 请求相关信息
                request_url=enhanced_info.get('request_url'),
                request_method=enhanced_info.get('request_method'),
                request_params=enhanced_info.get('request_params'),
                response_status=enhanced_info.get('response_status'),
                response_time=enhanced_info.get('response_time'),

                # 用户会话信息
                session_id=enhanced_info.get('session_id'),
                user_ip=enhanced_info.get('user_ip'),
                user_location=enhanced_info.get('user_location'),

                # 应用状态信息
                app_state=enhanced_info.get('app_state'),
                memory_usage=enhanced_info.get('memory_usage'),
                battery_level=enhanced_info.get('battery_level')
            )
            error_reports.append(error_report)

        # 批量插入
        db.add_all(error_reports)
        db.commit()

        logger.info(f"批量错误上报成功: {len(error_reports)} 条")

        # 返回客户端期望的格式
        return {
            "success": True,
            "message": f"批量错误上报成功，共 {len(error_reports)} 条",
            "data": {
                "batch_size": len(error_reports),
                "timestamp": datetime.now().isoformat()
            }
        }

    except Exception:
        logger.exception("批量错误上报异常")
        db.rollback()
        # 错误上报失败不应该抛出异常，避免影响用户体验
        return {
            "success": False,
            "message": "批量错误上报失败，但不影响正常使用",
            "data": None
        }

@router.get("/stats", response_model=ErrorStatsResponse)
async def get_error_stats(
    admin_user: User = Depends(require_admin),
    db: Session = Depends(get_db),
    days: int = Query(7, ge=1, le=30, description="统计天数"),
    limit: int = Query(10, ge=1, le=50, description="最近错误数量限制")
):
    """
    获取错误统计信息（需要管理员权限，用于调试）
    """
    try:
        # 计算时间范围
        end_time = datetime.now()
        start_time = end_time - timedelta(days=days)

        # 查询时间范围内的错误
        query = db.query(ErrorReport).filter(
            ErrorReport.created_at >= start_time,
            ErrorReport.created_at <= end_time
        )

        # 总错误数
        total_errors = query.count()

        # 按错误类型统计
        error_type_stats = db.query(
            ErrorReport.error_type,
            func.count(ErrorReport.id).label('count')
        ).filter(
            ErrorReport.created_at >= start_time,
            ErrorReport.created_at <= end_time
        ).group_by(ErrorReport.error_type).all()

        error_types = {stat.error_type: stat.count for stat in error_type_stats}

        # 最近错误列表
        recent_errors = query.order_by(desc(ErrorReport.created_at)).limit(limit).all()
        recent_error_list = [ErrorReportInfo.model_validate(error) for error in recent_errors]

        return ErrorStatsResponse(
            total_errors=total_errors,
            error_types=error_types,
            recent_errors=recent_error_list,
            time_range={
                "start_time": start_time.isoformat(),
                "end_time": end_time.isoformat(),
                "days": days
            }
        )

    except Exception:
        logger.exception("获取错误统计异常")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取错误统计失败"
        )

@router.get("", response_model=ListResponse)
async def get_error_reports(
    admin_user: User = Depends(require_admin),
    db: Session = Depends(get_db),
    error_type: Optional[str] = Query(None, description="错误类型筛选"),
    days: int = Query(7, ge=1, le=30, description="查询天数"),
    limit: int = Query(20, ge=1, le=100, description="返回数量限制"),
    offset: int = Query(0, ge=0, description="偏移量")
):
    """
    获取错误报告列表（需要管理员权限，用于调试）
    """
    try:
        # 计算时间范围
        end_time = datetime.now()
        start_time = end_time - timedelta(days=days)

        # 构建查询
        query = db.query(ErrorReport).filter(
            ErrorReport.created_at >= start_time,
            ErrorReport.created_at <= end_time
        )

        # 添加错误类型筛选
        if error_type:
            query = query.filter(ErrorReport.error_type == error_type)

        # 获取总数
        total = query.count()

        # 分页查询
        error_reports = query.order_by(desc(ErrorReport.created_at)).offset(offset).limit(limit).all()

        # 转换为响应模型
        error_list = [ErrorReportInfo.model_validate(error) for error in error_reports]

        return ListResponse(
            total=total,
            items=error_list
        )

    except Exception:
        logger.exception("获取错误报告列表异常")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取错误报告列表失败"
        )
