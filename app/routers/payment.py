"""
支付相关API路由
"""
from fastapi import APIRouter, HTTPException, Depends, status, Request
from fastapi.responses import JSONResponse
from sqlalchemy.orm import Session
from typing import List, Optional
import logging

from app.database import get_db
from app.models import User
from app.models.payment import OrderStatus
from app.schemas.payment import (
    MembershipPlanInfo, MembershipPlanListResponse,
    OrderCreate, OrderInfo, OrderListResponse, OrderCancelRequest,
    WeChatPayRequest, WeChatPayResponse,
    PaymentResponse
)
from app.services.payment_service import payment_service
from app.auth import get_current_user
from app.dependencies import MerchantContext, get_merchant_context

logger = logging.getLogger(__name__)

router = APIRouter(
    prefix="/payment",
    tags=["支付"],
    responses={404: {"description": "未找到"}},
)


@router.get("/plans", response_model=MembershipPlanListResponse)
async def get_membership_plans(
    active_only: bool = True,
    db: Session = Depends(get_db),
    merchant_context: MerchantContext = Depends(get_merchant_context)
):
    """
    获取会员套餐列表
    """
    try:
        plans = payment_service.get_membership_plans(db, merchant_context.merchant_id, active_only)
        
        plan_infos = []
        for plan in plans:
            plan_info = MembershipPlanInfo.model_validate(plan)
            plan_infos.append(plan_info)
        
        return MembershipPlanListResponse(
            plans=plan_infos,
            total=len(plan_infos)
        )
        
    except Exception as e:
        logger.exception("获取会员套餐列表异常")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取会员套餐列表失败"
        )


@router.post("/create-order", response_model=OrderInfo)
async def create_order(
    order_data: OrderCreate,
    request: Request,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
    merchant_context: MerchantContext = Depends(get_merchant_context)
):
    """
    创建订单
    """
    try:
        # 获取客户端信息
        client_ip = request.client.host if request.client else None
        user_agent = request.headers.get("User-Agent")

        # 创建订单
        order = payment_service.create_order(
            db=db,
            user_id=current_user.id,
            plan_id=order_data.plan_id,
            merchant_id=merchant_context.merchant_id,
            client_ip=client_ip or order_data.client_ip,
            user_agent=user_agent or order_data.user_agent
        )
        
        # 获取套餐信息用于响应
        plan = payment_service.get_membership_plan(db, order.plan_id, merchant_context.merchant_id)
        
        order_info = OrderInfo.model_validate(order)
        order_info.plan_name = plan.name if plan else None
        
        return order_info
        
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.exception("创建订单异常")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="创建订单失败"
        )


@router.get("/orders", response_model=OrderListResponse)
async def get_user_orders(
    status_filter: Optional[OrderStatus] = None,
    limit: int = 20,
    offset: int = 0,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
    merchant_context: MerchantContext = Depends(get_merchant_context)
):
    """
    获取用户订单列表
    """
    try:
        # 限制查询数量
        limit = min(limit, 100)

        orders, total = payment_service.get_user_orders(
            db=db,
            user_id=current_user.id,
            merchant_id=merchant_context.merchant_id,
            status=status_filter,
            limit=limit,
            offset=offset
        )
        
        order_infos = []
        for order in orders:
            # 获取套餐信息
            plan = payment_service.get_membership_plan(db, order.plan_id, merchant_context.merchant_id)
            
            order_info = OrderInfo.model_validate(order)
            order_info.plan_name = plan.name if plan else None
            order_infos.append(order_info)
        
        return OrderListResponse(
            orders=order_infos,
            total=total,
            has_more=offset + len(orders) < total
        )
        
    except Exception as e:
        logger.exception("获取用户订单列表异常")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取订单列表失败"
        )


@router.get("/order/{order_id}", response_model=OrderInfo)
async def get_order_detail(
    order_id: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    获取订单详情
    """
    try:
        order = payment_service.get_order(db, order_id, current_user.id)
        if not order:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="订单不存在"
            )
        
        # 获取套餐信息
        plan = payment_service.get_membership_plan(db, order.plan_id)
        
        order_info = OrderInfo.model_validate(order)
        order_info.plan_name = plan.name if plan else None
        
        return order_info
        
    except HTTPException:
        raise
    except Exception as e:
        logger.exception("获取订单详情异常")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取订单详情失败"
        )


@router.post("/cancel-order/{order_id}", response_model=PaymentResponse)
async def cancel_order(
    order_id: str,
    cancel_data: OrderCancelRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    取消订单
    """
    try:
        success = payment_service.cancel_order(
            db=db,
            order_id=order_id,
            user_id=current_user.id,
            reason=cancel_data.cancel_reason
        )
        
        if success:
            return PaymentResponse(
                success=True,
                message="订单取消成功"
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="订单取消失败"
            )
            
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.exception("取消订单异常")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="取消订单失败"
        )


@router.post("/wechat-pay", response_model=WeChatPayResponse)
async def create_wechat_payment(
    pay_request: WeChatPayRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    创建微信支付
    """
    try:
        pay_params = payment_service.create_wechat_payment(
            db=db,
            order_id=pay_request.order_id,
            user_id=current_user.id
        )
        
        return WeChatPayResponse(**pay_params)
        
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.exception("创建微信支付异常")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="创建微信支付失败"
        )


@router.post("/callback")
async def payment_callback(
    request: Request
):
    """
    微信支付回调（通用）
    """
    return await _handle_payment_callback(request)


@router.post("/callback/{merchant_code}")
async def payment_callback_with_merchant(
    request: Request,
    merchant_code: str
):
    """
    微信支付回调（指定商户）
    """
    # 将商户代码添加到请求状态，供中间件使用
    request.state.callback_merchant_code = merchant_code
    return await _handle_payment_callback(request)


async def _handle_payment_callback(request: Request):
    """处理支付回调的通用逻辑"""
    try:
        # 获取请求头和请求体
        headers = dict(request.headers)
        body = await request.body()
        body_str = body.decode('utf-8')

        # 获取客户端IP
        client_ip = request.client.host if request.client else None

        # 获取商户代码（如果有）
        merchant_code = getattr(request.state, 'callback_merchant_code', None)

        logger.info(f"收到支付回调，来源IP: {client_ip}, 商户: {merchant_code}")

        # 使用回调处理器处理
        from app.services.callback_handler import callback_handler
        result = await callback_handler.handle_payment_callback(headers, body_str, client_ip)

        # 根据处理结果返回响应
        if result["code"] == "SUCCESS":
            return JSONResponse(
                status_code=200,
                content=result
            )
        else:
            return JSONResponse(
                status_code=400,
                content=result
            )

    except Exception as e:
        logger.exception("处理支付回调异常")
        return JSONResponse(
            status_code=500,
            content={"code": "FAIL", "message": "服务器错误"}
        )


@router.get("/query-order/{order_id}")
async def query_wechat_order(
    order_id: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    查询微信支付订单状态
    """
    try:
        # 验证订单归属
        order = payment_service.get_order(db, order_id, current_user.id)
        if not order:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="订单不存在"
            )

        # 查询微信订单状态
        from app.services.wechat_pay_service import wechat_pay_service
        if not wechat_pay_service:
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="微信支付服务不可用"
            )

        wechat_order = wechat_pay_service.query_order(order_id)

        return PaymentResponse(
            success=True,
            message="查询成功",
            data=wechat_order
        )

    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.exception("查询微信订单异常")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="查询订单状态失败"
        )
