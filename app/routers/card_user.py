"""
卡密用户API接口 - 用户兑换和查询
"""
from fastapi import APIRouter, Depends, HTTPException, status, Request
from sqlalchemy.orm import Session
import math

from app.database import get_db
from app.auth.base import get_current_user
from app.models.user import User
from app.services.card_service import CardService
from app.schemas.card import (
    CardRedeemRequest, CardRedeemResponse, UserBenefitSummary,
    CardUsageHistoryResponse, CardUsageRecordInfo
)

router = APIRouter(prefix="/cards", tags=["卡密兑换"])


@router.post("/redeem", response_model=CardRedeemResponse)
async def redeem_card(
    redeem_request: CardRedeemRequest,
    request: Request,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    兑换卡密
    """
    try:
        # 获取客户端信息
        ip_address = request.client.host if request.client else None
        user_agent = request.headers.get("user-agent")
        
        # 兑换卡密
        success, message, benefit_info = CardService.redeem_card(
            db=db,
            user=current_user,
            redeem_request=redeem_request,
            ip_address=ip_address,
            user_agent=user_agent
        )
        
        if not success:
            return CardRedeemResponse(
                success=False,
                message=message
            )
        
        # 构建成功响应
        response_data = {
            "success": True,
            "message": message
        }
        
        if benefit_info:
            response_data.update({
                "benefit_type": benefit_info.get("benefit_type"),
                "benefit_value": benefit_info.get("benefit_value"),
                "benefit_unit": benefit_info.get("benefit_unit"),
                "benefit_description": benefit_info.get("benefit_description")
            })
        
        return CardRedeemResponse(**response_data)
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"兑换卡密失败: {str(e)}"
        )


@router.get("/benefits", response_model=UserBenefitSummary)
async def get_user_benefits(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    获取用户权益汇总
    """
    try:
        benefits = CardService.get_user_benefits(db=db, user_id=current_user.id)
        return benefits
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取用户权益失败: {str(e)}"
        )


@router.get("/history", response_model=CardUsageHistoryResponse)
async def get_card_usage_history(
    page: int = 1,
    page_size: int = 20,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    获取用户卡密使用历史
    """
    try:
        # 参数验证
        if page < 1:
            page = 1
        if page_size < 1 or page_size > 100:
            page_size = 20
        
        # 获取使用历史
        records, total = CardService.get_user_card_usage_history(
            db=db,
            user_id=current_user.id,
            page=page,
            page_size=page_size
        )
        
        total_pages = math.ceil(total / page_size) if total > 0 else 1
        
        return CardUsageHistoryResponse(
            items=[CardUsageRecordInfo.from_orm(record) for record in records],
            total=total,
            page=page,
            page_size=page_size,
            total_pages=total_pages
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取使用历史失败: {str(e)}"
        )


@router.get("/validate/{card_code}")
async def validate_card_code(
    card_code: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    验证卡密有效性（不实际兑换）
    """
    try:
        is_valid, message, card = CardService.validate_card(db=db, card_code=card_code)
        
        response = {
            "valid": is_valid,
            "message": message
        }
        
        if is_valid and card:
            # 获取批次信息
            from app.models.card import CardBatch
            batch = db.query(CardBatch).filter(CardBatch.id == card.batch_id).first()
            
            if batch:
                response.update({
                    "benefit_type": batch.benefit_type,
                    "benefit_value": batch.benefit_value,
                    "benefit_unit": batch.benefit_unit,
                    "benefit_description": batch.benefit_description,
                    "expires_at": card.expires_at.isoformat() if card.expires_at else None
                })
        
        return response
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"验证卡密失败: {str(e)}"
        )


@router.get("/quota/check")
async def check_quota_availability(
    feature_name: str,
    quota_needed: int = 1,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    检查用户是否有足够的次数权益
    """
    try:
        # 获取用户权益汇总
        benefits = CardService.get_user_benefits(db=db, user_id=current_user.id)
        
        # 检查是否有有效会员（会员优先）
        if benefits.has_active_membership:
            return {
                "available": True,
                "source": "membership",
                "message": "会员权益可用",
                "membership_expires_at": benefits.membership_expires_at.isoformat() if benefits.membership_expires_at else None
            }
        
        # 检查次数权益
        if benefits.total_quota >= quota_needed:
            return {
                "available": True,
                "source": "quota",
                "message": f"次数权益可用，剩余 {benefits.total_quota} 次",
                "remaining_quota": benefits.total_quota
            }
        
        return {
            "available": False,
            "source": None,
            "message": f"权益不足，需要 {quota_needed} 次，剩余 {benefits.total_quota} 次",
            "remaining_quota": benefits.total_quota
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"检查权益失败: {str(e)}"
        )


@router.post("/quota/consume")
async def consume_quota(
    feature_name: str,
    quota_needed: int = 1,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    消费用户次数权益
    """
    try:
        # 检查是否有有效会员（会员不消费次数权益）
        benefits = CardService.get_user_benefits(db=db, user_id=current_user.id)
        
        if benefits.has_active_membership:
            return {
                "consumed": False,
                "source": "membership",
                "message": "使用会员权益，无需消费次数",
                "remaining_quota": benefits.total_quota
            }
        
        # 消费次数权益
        success = CardService.consume_quota_benefit(
            db=db,
            user_id=current_user.id,
            feature_name=feature_name,
            quota_needed=quota_needed
        )
        
        if success:
            # 重新获取剩余权益
            updated_benefits = CardService.get_user_benefits(db=db, user_id=current_user.id)
            
            return {
                "consumed": True,
                "source": "quota",
                "message": f"成功消费 {quota_needed} 次权益",
                "remaining_quota": updated_benefits.total_quota
            }
        else:
            return {
                "consumed": False,
                "source": "quota",
                "message": "次数权益不足",
                "remaining_quota": benefits.total_quota
            }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"消费权益失败: {str(e)}"
        )
