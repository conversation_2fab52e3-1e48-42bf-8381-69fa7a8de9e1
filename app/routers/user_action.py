"""
用户行为记录API路由
"""
from fastapi import APIRouter, HTTPException, Depends, status, Query
from sqlalchemy.orm import Session
from typing import List, Optional
import logging

from app.database import get_db
from app.models import User, UserAction
from app.schemas.user import (
    UserActionCreate,
    UserActionInfo,
    ListResponse,
    ActionTypeEnum
)
from app.auth import get_current_user, require_admin
from app.services.user_action_service import user_action_service

logger = logging.getLogger(__name__)

router = APIRouter(
    prefix="/user/action",
    tags=["用户行为"],
    responses={404: {"description": "未找到"}},
)

@router.post("/report", response_model=dict)
async def report_user_action(
    action_data: UserActionCreate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    前端主动上报用户操作 - 纯粹的操作记录

    用于记录用户的关键操作行为，供管理员统计分析使用
    不涉及会员权益消费，只做行为记录
    """
    try:
        # 使用用户操作服务记录操作
        result = user_action_service.record_user_action(
            db=db,
            user_id=current_user.id,
            action_type=action_data.action_type.value,
            action_content=action_data.action_content,
            resource_type=action_data.resource_type,
            resource_id=action_data.resource_id,
            file_size=action_data.file_size,
            file_format=action_data.file_format,
            operation_status=action_data.operation_status or "completed",
            error_message=action_data.error_message,
            client_info=action_data.client_info,
            ip_address=action_data.ip_address,
            user_agent=action_data.user_agent,
            template_id=action_data.template_id
        )

        if result["success"]:
            logger.info(f"用户 {current_user.id} 操作记录成功: {action_data.action_type}")
            return result
        else:
            logger.error(f"用户 {current_user.id} 操作记录失败: {result['message']}")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result["message"]
            )

    except Exception as e:
        logger.exception("记录用户行为异常")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"记录用户行为失败: {str(e)}"
        )


@router.get("/stats")
async def get_user_action_stats(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
    days: int = Query(30, ge=1, le=365, description="统计天数")
):
    """
    获取用户操作统计
    """
    try:
        stats = user_action_service.get_user_action_stats(
            db=db,
            user_id=current_user.id,
            days=days
        )

        return {
            "success": True,
            "data": stats,
            "message": "获取统计信息成功"
        }

    except Exception as e:
        logger.exception("获取用户操作统计异常")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取用户操作统计失败"
        )


@router.get("/admin/stats")
async def get_admin_action_stats(
    admin_user: User = Depends(require_admin),
    db: Session = Depends(get_db),
    days: int = Query(30, ge=1, le=365, description="统计天数"),
    limit: int = Query(100, ge=1, le=1000, description="返回用户数量限制")
):
    """
    获取管理员用的全局操作统计（需要管理员权限）
    """
    try:

        stats = user_action_service.get_admin_action_stats(
            db=db,
            days=days,
            limit=limit
        )

        return {
            "success": True,
            "data": stats,
            "message": "获取管理员统计信息成功"
        }

    except Exception as e:
        logger.exception("获取管理员统计异常")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取管理员统计失败"
        )

# @router.get("", response_model=ListResponse)
# async def get_user_actions(
#     current_user: User = Depends(get_current_user),
#     db: Session = Depends(get_db),
#     action_type: Optional[ActionTypeEnum] = Query(None, description="行为类型筛选"),
#     template_id: Optional[str] = Query(None, description="模板ID筛选"),
#     limit: int = Query(20, ge=1, le=100, description="返回数量限制"),
#     offset: int = Query(0, ge=0, description="偏移量")
# ):
#     """
#     获取用户行为记录列表
#     """
#     try:
#         # 构建查询
#         query = db.query(UserAction).filter(UserAction.user_id == current_user.id)
        
#         # 添加筛选条件
#         if action_type:
#             query = query.filter(UserAction.action_type == action_type.value)
        
#         if template_id:
#             query = query.filter(UserAction.template_id == template_id)
        
#         # 获取总数
#         total = query.count()
        
#         # 分页查询
#         actions = query.order_by(UserAction.created_at.desc()).offset(offset).limit(limit).all()
        
#         # 转换为响应模型
#         action_list = [UserActionInfo.model_validate(action) for action in actions]
        
#         return ListResponse(
#             total=total,
#             items=action_list
#         )
        
#     except Exception as e:
#         logger.exception("获取用户行为记录异常")
#         raise HTTPException(
#             status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
#             detail="获取用户行为记录失败"
#         )

# @router.get("/stats")
# async def get_user_action_stats(
#     current_user: User = Depends(get_current_user),
#     db: Session = Depends(get_db)
# ):
#     """
#     获取用户行为统计
#     """
#     try:
#         # 统计各种行为类型的数量
#         stats = {}
        
#         for action_type in ActionTypeEnum:
#             count = db.query(UserAction).filter(
#                 UserAction.user_id == current_user.id,
#                 UserAction.action_type == action_type.value
#             ).count()
#             stats[action_type.value] = count
        
#         # 统计模板使用情况
#         template_stats = db.query(
#             UserAction.template_id,
#             db.func.count(UserAction.id).label('count')
#         ).filter(
#             UserAction.user_id == current_user.id,
#             UserAction.template_id.isnot(None)
#         ).group_by(UserAction.template_id).all()
        
#         template_usage = {template_id: count for template_id, count in template_stats}
        
#         return {
#             "action_stats": stats,
#             "template_usage": template_usage,
#             "total_actions": sum(stats.values())
#         }
        
#     except Exception as e:
#         logger.exception("获取用户行为统计异常")
#         raise HTTPException(
#             status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
#             detail="获取用户行为统计失败"
#         )
