"""
增强的权限检查API - 集成卡密权益和会员权益
"""
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import Optional

from app.database import get_db
from app.auth.base import get_current_user
from app.models.user import User
from app.services.enhanced_permission_service import enhanced_permission_service

router = APIRouter(prefix="/permissions", tags=["权限检查"])


@router.get("/check/{feature}")
async def check_feature_permission(
    feature: str,
    quota_needed: int = 1,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    检查功能权限 - 集成卡密权益
    
    支持的功能：
    - resume_export: 简历导出
    - idphoto_generate: 证件照生成
    - premium_templates: 高级模板
    """
    try:
        permission_result = enhanced_permission_service.check_feature_permission(
            db=db,
            user_id=current_user.id,
            feature=feature,
            quota_needed=quota_needed,
            check_usage=True
        )
        
        return permission_result
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"权限检查失败: {str(e)}"
        )


@router.post("/consume/{feature}")
async def consume_feature_permission(
    feature: str,
    quota_needed: int = 1,
    template_id: Optional[str] = None,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    消费功能权限
    
    根据用户权益来源自动选择消费方式：
    1. 会员权益：不消费次数，直接使用
    2. 卡密次数权益：消费对应次数
    3. 免费额度：按原有逻辑处理
    """
    try:
        result = enhanced_permission_service.consume_feature_permission(
            db=db,
            user_id=current_user.id,
            feature=feature,
            quota_needed=quota_needed,
            action_content={
                "feature": feature,
                "quota_needed": quota_needed,
                "template_id": template_id
            },
            template_id=template_id
        )
        
        if not result["success"]:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail={
                    "error": "permission_denied",
                    "message": result["message"],
                    "feature": feature,
                    "quota_needed": quota_needed
                }
            )
        
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"权限消费失败: {str(e)}"
        )


@router.get("/summary")
async def get_user_permission_summary(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    获取用户权限汇总
    
    包含：
    - 会员信息
    - 卡密权益信息
    - 各功能权限状态
    """
    try:
        summary = enhanced_permission_service.get_user_permission_summary(
            db=db,
            user_id=current_user.id
        )
        
        return summary
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取权限汇总失败: {str(e)}"
        )


@router.get("/benefits/detailed")
async def get_detailed_benefits(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    获取详细的权益信息
    
    包含：
    - 会员权益详情
    - 卡密权益详情
    - 使用历史统计
    """
    try:
        from app.services.card_service import CardService
        from app.services.membership_service import MembershipService
        
        # 获取卡密权益
        card_benefits = CardService.get_user_benefits(db=db, user_id=current_user.id)
        
        # 获取会员信息
        membership_service = MembershipService()
        membership_info = membership_service.get_user_membership_info(db, current_user.id)
        
        # 获取使用历史统计
        from app.models.user import UserAction
        from sqlalchemy import func, and_
        from datetime import datetime, timedelta
        
        # 今日使用统计
        today = datetime.now().date()
        today_usage = db.query(
            UserAction.action_type,
            func.count(UserAction.id).label('count')
        ).filter(
            and_(
                UserAction.user_id == current_user.id,
                func.date(UserAction.created_at) == today
            )
        ).group_by(UserAction.action_type).all()
        
        # 本月使用统计
        month_start = datetime.now().replace(day=1, hour=0, minute=0, second=0, microsecond=0)
        month_usage = db.query(
            UserAction.action_type,
            func.count(UserAction.id).label('count')
        ).filter(
            and_(
                UserAction.user_id == current_user.id,
                UserAction.created_at >= month_start
            )
        ).group_by(UserAction.action_type).all()
        
        return {
            "membership_info": membership_info,
            "card_benefits": {
                "total_quota": card_benefits.total_quota,
                "quota_benefits": [
                    {
                        "id": benefit.id,
                        "benefit_type": benefit.benefit_type,
                        "remaining_quota": benefit.remaining_quota,
                        "expires_at": benefit.expires_at.isoformat() if benefit.expires_at else None,
                        "source": benefit.source,
                        "source_id": benefit.source_id,
                        "created_at": benefit.created_at.isoformat()
                    }
                    for benefit in card_benefits.quota_benefits
                ],
                "membership_benefits": [
                    {
                        "id": benefit.id,
                        "benefit_type": benefit.benefit_type,
                        "expires_at": benefit.expires_at.isoformat() if benefit.expires_at else None,
                        "source": benefit.source,
                        "source_id": benefit.source_id,
                        "created_at": benefit.created_at.isoformat()
                    }
                    for benefit in card_benefits.membership_benefits
                ],
                "has_active_membership": card_benefits.has_active_membership,
                "membership_expires_at": card_benefits.membership_expires_at.isoformat() if card_benefits.membership_expires_at else None
            },
            "usage_statistics": {
                "today": {action_type: count for action_type, count in today_usage},
                "this_month": {action_type: count for action_type, count in month_usage}
            },
            "benefit_priority": {
                "primary_source": (
                    "membership" if card_benefits.has_active_membership
                    else "card_quota" if card_benefits.total_quota > 0
                    else "free_limit"
                ),
                "description": (
                    "当前使用会员权益，享受无限制使用" if card_benefits.has_active_membership
                    else f"当前使用卡密次数权益，剩余 {card_benefits.total_quota} 次" if card_benefits.total_quota > 0
                    else "当前使用免费额度，建议升级会员或兑换卡密"
                )
            }
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取详细权益信息失败: {str(e)}"
        )


@router.get("/features/available")
async def get_available_features(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    获取用户可用的功能列表
    """
    try:
        features = ["resume_export", "idphoto_generate", "premium_templates"]
        available_features = []
        
        for feature in features:
            permission = enhanced_permission_service.check_feature_permission(
                db=db,
                user_id=current_user.id,
                feature=feature,
                quota_needed=1,
                check_usage=True
            )
            
            if permission["has_permission"]:
                available_features.append({
                    "feature": feature,
                    "source": permission.get("source", "unknown"),
                    "reason": permission.get("reason", ""),
                    "usage_info": permission.get("usage_info", {})
                })
        
        return {
            "available_features": available_features,
            "total_available": len(available_features)
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取可用功能失败: {str(e)}"
        )
