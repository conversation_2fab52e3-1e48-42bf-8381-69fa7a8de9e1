"""
商户上下文中间件
"""
import logging
from typing import Optional, Dict, Any
from fastapi import Request, HTTPException, status
from fastapi.responses import JSONResponse
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.responses import Response
from contextvars import ContextVar
from sqlalchemy.orm import Session

from app.database import SessionLocal
from app.services.merchant_service import merchant_service

logger = logging.getLogger(__name__)

# 商户上下文变量
merchant_context: ContextVar[Optional[Dict[str, Any]]] = ContextVar('merchant_context', default=None)


class MerchantContextMiddleware(BaseHTTPMiddleware):
    """商户上下文中间件"""
    
    def __init__(self, app):
        super().__init__(app)
        # 不需要商户验证的路径
        self.exempt_paths = {
            "/docs",
            "/redoc", 
            "/openapi.json",
            "/health",
            "/favicon.ico"
        }
    
    async def dispatch(self, request: Request, call_next) -> Response:
        """处理请求"""
        # 检查是否为豁免路径
        if any(request.url.path.startswith(path) for path in self.exempt_paths):
            return await call_next(request)
        
        # 获取小程序来源标识
        source_code = self._extract_source_code(request)

        # 获取数据库会话
        db = SessionLocal()
        try:
            # 通过source获取商户配置
            merchant_config = await self._get_merchant_by_source(db, source_code, request)
            if not merchant_config:
                return JSONResponse(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    content={"detail": f"无效的小程序来源标识: {source_code}"}
                )
            
            # 设置商户上下文
            merchant_context.set(merchant_config)
            
            # 将商户信息添加到请求状态
            request.state.source_code = source_code
            request.state.merchant_code = merchant_config["merchant"]["code"]
            request.state.merchant_config = merchant_config
            request.state.merchant_id = merchant_config["merchant"]["id"]
            
            # 继续处理请求
            response = await call_next(request)
            
            return response
            
        except Exception as e:
            logger.exception("商户中间件处理异常")
            return JSONResponse(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                content={"detail": "商户验证失败"}
            )
        finally:
            db.close()
            # 清除上下文
            merchant_context.set(None)
    
    def _extract_source_code(self, request: Request) -> str:
        """提取小程序来源标识"""
        # 1. 优先从查询参数获取
        source = request.query_params.get("source")
        if source:
            return source

        # 2. 从请求头获取
        source = request.headers.get("X-Merchant-Source")
        if source:
            return source

        # 3. 从请求体获取（仅POST请求）
        if request.method == "POST":
            # 注意：这里不能直接读取body，因为会影响后续处理
            # 实际实现中可能需要更复杂的逻辑
            pass

        # 4. 从路径参数获取
        path_parts = request.url.path.strip("/").split("/")
        if len(path_parts) > 1 and path_parts[0] == "merchant":
            return path_parts[1]

        # 5. 使用默认来源
        return "default"
    
    async def _get_merchant_by_source(
        self,
        db: Session,
        source_code: str,
        request: Request
    ) -> Optional[Dict[str, Any]]:
        """通过小程序来源获取商户配置"""
        try:
            # 获取客户端IP
            client_ip = self._get_client_ip(request)

            # 通过source获取商户配置
            merchant_config = merchant_service.get_merchant_by_source(db, source_code)
            if not merchant_config:
                logger.warning(f"小程序来源映射不存在: {source_code}")
                return None

            # 验证商户访问权限
            merchant_code = merchant_config["merchant"]["code"]
            if not merchant_service.validate_merchant_access(db, merchant_code, client_ip):
                logger.warning(f"商户访问验证失败: {merchant_code}, IP: {client_ip}")
                return None

            return merchant_config

        except Exception as e:
            logger.error(f"通过source获取商户配置异常: {source_code}, 错误: {e}")
            return None
    
    def _get_client_ip(self, request: Request) -> Optional[str]:
        """获取客户端IP"""
        # 优先从X-Forwarded-For获取
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            return forwarded_for.split(",")[0].strip()
        
        # 从X-Real-IP获取
        real_ip = request.headers.get("X-Real-IP")
        if real_ip:
            return real_ip
        
        # 从客户端连接获取
        if request.client:
            return request.client.host
        
        return None


def get_current_merchant() -> Optional[Dict[str, Any]]:
    """获取当前商户上下文"""
    return merchant_context.get()


def get_current_merchant_id() -> Optional[int]:
    """获取当前商户ID"""
    merchant_config = get_current_merchant()
    if merchant_config:
        return merchant_config["merchant"]["id"]
    return None


def get_current_merchant_code() -> Optional[str]:
    """获取当前商户代码"""
    merchant_config = get_current_merchant()
    if merchant_config:
        return merchant_config["merchant"]["code"]
    return None


def get_current_wechat_config() -> Optional[Dict[str, Any]]:
    """获取当前商户微信配置"""
    merchant_config = get_current_merchant()
    if merchant_config:
        return merchant_config.get("wechat")
    return None


def require_merchant_context():
    """要求商户上下文的依赖函数"""
    merchant_config = get_current_merchant()
    if not merchant_config:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="缺少商户上下文"
        )
    return merchant_config


class MerchantContextManager:
    """商户上下文管理器（用于测试和特殊场景）"""
    
    def __init__(self, merchant_config: Dict[str, Any]):
        self.merchant_config = merchant_config
        self.token = None
    
    def __enter__(self):
        self.token = merchant_context.set(self.merchant_config)
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        if self.token:
            merchant_context.reset(self.token)


# 工具函数
def with_merchant_context(merchant_config: Dict[str, Any]):
    """装饰器：在指定商户上下文中执行函数"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            with MerchantContextManager(merchant_config):
                return func(*args, **kwargs)
        return wrapper
    return decorator


async def extract_merchant_from_request(request: Request) -> Optional[str]:
    """从请求中提取商户代码（工具函数）"""
    middleware = MerchantContextMiddleware(None)
    return middleware._extract_merchant_code(request)
