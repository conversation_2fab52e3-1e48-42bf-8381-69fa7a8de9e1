"""
商户相关的依赖注入函数
提供轻量级的商户信息获取方式，用于特定接口
"""
import logging
from typing import Optional, Dict, Any
from fastapi import Depends, HTTPException, status, Request, Query, Header
from sqlalchemy.orm import Session

from app.database import get_db
from app.services.merchant_service import merchant_service

logger = logging.getLogger(__name__)


def get_source_code(
    source: Optional[str] = Query(None, description="小程序来源标识"),
    x_merchant_source: Optional[str] = Header(None, alias="X-Merchant-Source", description="请求头中的商户来源")
) -> str:
    """
    获取小程序来源标识
    优先级：查询参数 > 请求头 > 默认值
    """
    if source:
        return source
    if x_merchant_source:
        return x_merchant_source
    return "default"


def get_merchant_config(
    source_code: str = Depends(get_source_code),
    db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """
    获取商户配置（依赖注入版本）
    用于需要商户信息的特定接口
    """
    try:
        # 通过source获取商户配置
        merchant_config = merchant_service.get_merchant_by_source(db, source_code)
        if not merchant_config:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"无效的小程序来源标识: {source_code}"
            )
        
        return merchant_config
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取商户配置异常: {source_code}, 错误: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取商户配置失败"
        )


def get_merchant_id(
    merchant_config: Dict[str, Any] = Depends(get_merchant_config)
) -> int:
    """获取商户ID"""
    return merchant_config["merchant"]["id"]


def get_merchant_code(
    merchant_config: Dict[str, Any] = Depends(get_merchant_config)
) -> str:
    """获取商户代码"""
    return merchant_config["merchant"]["code"]


def get_wechat_config(
    merchant_config: Dict[str, Any] = Depends(get_merchant_config)
) -> Dict[str, Any]:
    """获取微信配置"""
    wechat_config = merchant_config.get("wechat")
    if not wechat_config:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="商户微信配置不存在"
        )
    return wechat_config


def get_source_info(
    merchant_config: Dict[str, Any] = Depends(get_merchant_config)
) -> Dict[str, Any]:
    """获取小程序来源信息"""
    source_info = merchant_config.get("source")
    if not source_info:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="小程序来源信息不存在"
        )
    return source_info


# 可选的依赖注入函数（不抛出异常）
def get_merchant_config_optional(
    source_code: str = Depends(get_source_code),
    db: Session = Depends(get_db)
) -> Optional[Dict[str, Any]]:
    """
    获取商户配置（可选版本）
    如果获取失败返回None而不是抛出异常
    """
    try:
        return merchant_service.get_merchant_by_source(db, source_code)
    except Exception as e:
        logger.warning(f"获取商户配置失败: {source_code}, 错误: {e}")
        return None


def get_wechat_config_optional(
    merchant_config: Optional[Dict[str, Any]] = Depends(get_merchant_config_optional)
) -> Optional[Dict[str, Any]]:
    """获取微信配置（可选版本）"""
    if not merchant_config:
        return None
    return merchant_config.get("wechat")


# 兼容性函数，保持与中间件的接口一致
def require_merchant_context(
    merchant_config: Dict[str, Any] = Depends(get_merchant_config)
) -> Dict[str, Any]:
    """要求商户上下文的依赖函数（兼容性）"""
    return merchant_config
