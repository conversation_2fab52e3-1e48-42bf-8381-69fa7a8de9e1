"""
管理员权限认证模块
"""
from typing import Optional, Union
from fastapi import HTTPException, status, Depends, Request
from fastapi.security import HTT<PERSON><PERSON>ear<PERSON>, HTTPAuthorizationCredentials
from sqlalchemy.orm import Session
import logging

from app.database import get_db
from app.models import User
from app.auth.base import get_current_user, get_current_user_optional
from config.fastapi_config import settings

logger = logging.getLogger(__name__)

# HTTP Bearer认证（用于API密钥）
security = HTTPBearer(auto_error=False)


def verify_admin_api_key(request: Request) -> bool:
    """
    验证管理员API密钥
    
    Args:
        request: FastAPI请求对象
        
    Returns:
        bool: 是否验证通过
    """
    # 从请求头获取API密钥
    admin_key = request.headers.get("X-Admin-Key")
    
    if not admin_key:
        return False
    
    # 验证API密钥
    expected_key = getattr(settings, 'ADMIN_API_KEY', None)
    if not expected_key:
        logger.warning("管理员API密钥未配置")
        return False
    
    return admin_key == expected_key


def verify_admin_user(user: Optional[User]) -> bool:
    """
    验证用户是否为管理员
    
    Args:
        user: 用户对象
        
    Returns:
        bool: 是否为管理员
    """
    if not user:
        return False
    
    # 检查用户是否有管理员权限
    return getattr(user, 'is_admin', False)


async def get_admin_user(
    request: Request,
    db: Session = Depends(get_db)
) -> User:
    """
    获取管理员用户（支持API密钥和用户角色双重验证）
    
    Args:
        request: FastAPI请求对象
        db: 数据库会话
        
    Returns:
        User: 管理员用户对象（API密钥验证时返回虚拟管理员用户）
        
    Raises:
        HTTPException: 权限验证失败
    """
    # 方式1：检查API密钥
    if verify_admin_api_key(request):
        logger.info("通过API密钥验证管理员权限")
        # 返回虚拟管理员用户对象
        virtual_admin = User()
        virtual_admin.id = -1
        virtual_admin.openid = "api_key_admin"
        virtual_admin.nickname = "API密钥管理员"
        virtual_admin.is_admin = True
        return virtual_admin
    
    # 方式2：检查用户角色
    current_user = get_current_user_optional(request, db)
    if verify_admin_user(current_user):
        logger.info(f"用户 {current_user.id} 通过角色验证管理员权限")
        return current_user
    
    # 权限验证失败
    logger.warning(f"管理员权限验证失败 - IP: {request.client.host}")
    raise HTTPException(
        status_code=status.HTTP_403_FORBIDDEN,
        detail="需要管理员权限"
    )


async def get_admin_user_optional(
    request: Request,
    db: Session = Depends(get_db)
) -> Optional[User]:
    """
    获取管理员用户（可选，不抛出异常）
    
    Args:
        request: FastAPI请求对象
        db: 数据库会话
        
    Returns:
        Optional[User]: 管理员用户对象，验证失败返回None
    """
    try:
        return await get_admin_user(request, db)
    except HTTPException:
        return None


def require_admin_permission(
    error_message: str = "需要管理员权限"
):
    """
    管理员权限检查装饰器工厂函数
    
    Args:
        error_message: 自定义错误消息
        
    Returns:
        装饰器函数
    """
    async def admin_permission_dependency(
        request: Request,
        db: Session = Depends(get_db)
    ) -> User:
        """管理员权限检查依赖"""
        try:
            return await get_admin_user(request, db)
        except HTTPException as e:
            # 使用自定义错误消息
            raise HTTPException(
                status_code=e.status_code,
                detail=error_message
            )
    
    return admin_permission_dependency


# 常用的管理员权限检查依赖
require_admin = require_admin_permission()


class AdminPermissionChecker:
    """
    管理员权限检查器类
    
    提供更灵活的权限检查方式，可以获取详细的权限信息
    """
    
    @staticmethod
    async def check_admin_permission(
        request: Request,
        db: Session = Depends(get_db)
    ) -> dict:
        """
        检查管理员权限并返回详细信息
        
        Returns:
            dict: 权限检查结果
        """
        try:
            admin_user = await get_admin_user(request, db)
            return {
                "has_permission": True,
                "is_admin": True,
                "admin_type": "api_key" if admin_user.id == -1 else "user_role",
                "admin_id": admin_user.id,
                "admin_name": admin_user.nickname or admin_user.openid,
                "message": "管理员权限验证成功"
            }
        except HTTPException as e:
            return {
                "has_permission": False,
                "is_admin": False,
                "admin_type": None,
                "admin_id": None,
                "admin_name": None,
                "message": e.detail,
                "status_code": e.status_code
            }


# 导出常用函数和类
__all__ = [
    "get_admin_user",
    "get_admin_user_optional", 
    "require_admin_permission",
    "require_admin",
    "AdminPermissionChecker",
    "verify_admin_api_key",
    "verify_admin_user"
]
