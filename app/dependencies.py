"""
业务逻辑依赖注入
负责按需从数据库查询商户信息和业务上下文
"""
import logging
from typing import Optional, Dict, Any
from fastapi import Depends, HTTPException, status, Request
from sqlalchemy.orm import Session
from pydantic import BaseModel

from app.database import get_db
from app.services.merchant_service import merchant_service

logger = logging.getLogger(__name__)


class MerchantContext(BaseModel):
    """商户上下文模型"""
    merchant_id: int
    merchant_code: str
    merchant_name: str
    app_id: str
    app_secret: str
    source_code: str
    source_name: Optional[str] = None
    
    class Config:
        from_attributes = True


def get_app_source(request: Request) -> str:
    """
    获取app source标识
    
    从中间件设置的request.state.app_source中获取
    如果不存在，抛出HTTP异常
    """
    app_source = getattr(request.state, 'app_source', None)
    
    if not app_source:
        logger.warning("请求中缺少app source标识")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="X-App-Source header or source query parameter is required."
        )
    
    return app_source


def get_merchant_context(
    request: Request,
    db: Session = Depends(get_db)
) -> MerchantContext:
    """
    获取商户上下文
    
    这是主要的业务逻辑依赖注入函数：
    1. 从request.state获取source标识
    2. 使用source查询数据库获取商户信息
    3. 返回完整的商户上下文
    
    Args:
        request: FastAPI请求对象
        db: 数据库会话
        
    Returns:
        MerchantContext: 包含商户信息的上下文对象
        
    Raises:
        HTTPException: 当source不存在或商户信息查询失败时
    """
    try:
        # 从中间件获取source标识
        app_source = getattr(request.state, 'app_source', None)
        
        if not app_source:
            logger.warning("请求中缺少app source标识")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="X-App-Source header or source query parameter is required."
            )
        
        # 使用source查询商户配置
        merchant_config = merchant_service.get_merchant_by_source(db, app_source)
        
        if not merchant_config:
            logger.warning(f"根据source未找到商户配置: {app_source}")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Source '{app_source}' not recognized or merchant not configured."
            )
        
        # 构建商户上下文
        context = MerchantContext(
            merchant_id=merchant_config["merchant"]["id"],
            merchant_code=merchant_config["merchant"]["code"],
            merchant_name=merchant_config["merchant"]["name"],
            app_id=merchant_config["wechat"]["app_id"],
            app_secret=merchant_config["wechat"]["app_secret"],
            source_code=merchant_config["source"]["source_code"],
            source_name=merchant_config["source"]["source_name"]
        )
        
        logger.debug(f"成功获取商户上下文: {app_source} -> {context.merchant_code}")
        return context
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取商户上下文异常: {app_source if 'app_source' in locals() else 'unknown'}, 错误: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve merchant context"
        )


def get_merchant_context_optional(
    request: Request,
    db: Session = Depends(get_db)
) -> Optional[MerchantContext]:
    """
    获取商户上下文（可选版本）
    
    与get_merchant_context类似，但失败时返回None而不是抛出异常
    适用于某些可选需要商户信息的场景
    """
    try:
        return get_merchant_context(request, db)
    except HTTPException:
        return None
    except Exception as e:
        logger.warning(f"获取可选商户上下文失败: {e}")
        return None


# 便捷的依赖注入函数
def get_merchant_id(
    merchant_context: MerchantContext = Depends(get_merchant_context)
) -> int:
    """获取商户ID"""
    return merchant_context.merchant_id


def get_merchant_code(
    merchant_context: MerchantContext = Depends(get_merchant_context)
) -> str:
    """获取商户代码"""
    return merchant_context.merchant_code


def get_wechat_config(
    merchant_context: MerchantContext = Depends(get_merchant_context)
) -> Dict[str, str]:
    """获取微信配置"""
    return {
        "app_id": merchant_context.app_id,
        "app_secret": merchant_context.app_secret
    }


def get_source_info(
    merchant_context: MerchantContext = Depends(get_merchant_context)
) -> Dict[str, str]:
    """获取来源信息"""
    return {
        "source_code": merchant_context.source_code,
        "source_name": merchant_context.source_name or ""
    }
