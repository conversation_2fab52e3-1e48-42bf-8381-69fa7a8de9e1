"""
卡密系统相关数据模型
"""
from sqlalchemy import Column, Integer, String, Text, TIMESTAMP, Boolean, DECIMAL, ForeignKey, Index
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from app.database import Base
import enum


class BenefitType(enum.Enum):
    """权益类型枚举"""
    QUOTA = "quota"          # 次数权益
    MEMBERSHIP = "membership"  # 会员时长权益


class CardStatus(enum.Enum):
    """卡密状态枚举"""
    UNUSED = "unused"        # 未使用
    USED = "used"           # 已使用
    EXPIRED = "expired"     # 已过期
    DISABLED = "disabled"   # 已禁用


class BatchStatus(enum.Enum):
    """批次状态枚举"""
    ACTIVE = "active"       # 激活
    DISABLED = "disabled"   # 禁用


class CardBatch(Base):
    """卡密批次表"""
    __tablename__ = "card_batches"

    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    merchant_id = Column(Integer, ForeignKey("merchants.id"), nullable=True, index=True, comment="商户ID")
    batch_name = Column(String(100), nullable=False, comment="批次名称")
    batch_code = Column(String(50), nullable=False, unique=True, index=True, comment="批次编码")
    
    # 权益配置
    benefit_type = Column(String(20), nullable=False, comment="权益类型：quota/membership")
    benefit_value = Column(Integer, nullable=False, comment="权益数值")
    benefit_unit = Column(String(20), nullable=False, comment="权益单位：times/days/months")
    benefit_description = Column(Text, nullable=True, comment="权益描述")
    
    # 批次统计
    total_cards = Column(Integer, default=0, nullable=False, comment="总卡密数量")
    used_cards = Column(Integer, default=0, nullable=False, comment="已使用数量")
    
    # 状态和有效期
    status = Column(String(20), default="active", nullable=False, comment="批次状态")
    expires_at = Column(TIMESTAMP, nullable=True, comment="批次过期时间")
    
    # 创建信息
    created_by = Column(Integer, nullable=True, comment="创建者用户ID")
    notes = Column(Text, nullable=True, comment="备注信息")
    
    created_at = Column(TIMESTAMP, server_default=func.current_timestamp(), comment="创建时间")
    updated_at = Column(
        TIMESTAMP,
        server_default=func.current_timestamp(),
        onupdate=func.current_timestamp(),
        comment="更新时间"
    )

    # 关联关系
    merchant = relationship("Merchant", back_populates="card_batches")
    cards = relationship("Card", back_populates="batch", cascade="all, delete-orphan")

    # 索引
    __table_args__ = (
        Index('idx_batch_merchant_status', 'merchant_id', 'status'),
        Index('idx_batch_benefit_type', 'benefit_type'),
        Index('idx_batch_created_at', 'created_at'),
    )


class Card(Base):
    """卡密表"""
    __tablename__ = "cards"

    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    batch_id = Column(Integer, ForeignKey("card_batches.id", ondelete="CASCADE"), nullable=False, comment="批次ID")
    card_code = Column(String(32), nullable=False, unique=True, index=True, comment="卡密码")
    
    # 状态信息
    status = Column(String(20), default="unused", nullable=False, comment="卡密状态")
    
    # 使用信息
    used_by = Column(Integer, ForeignKey("users.id"), nullable=True, comment="使用者用户ID")
    used_at = Column(TIMESTAMP, nullable=True, comment="使用时间")
    used_ip = Column(String(45), nullable=True, comment="使用IP地址")
    
    # 有效期
    expires_at = Column(TIMESTAMP, nullable=True, comment="卡密过期时间")
    
    created_at = Column(TIMESTAMP, server_default=func.current_timestamp(), comment="创建时间")
    updated_at = Column(
        TIMESTAMP,
        server_default=func.current_timestamp(),
        onupdate=func.current_timestamp(),
        comment="更新时间"
    )

    # 关联关系
    batch = relationship("CardBatch", back_populates="cards")
    user = relationship("User")
    usage_records = relationship("CardUsageRecord", back_populates="card", cascade="all, delete-orphan")

    # 索引
    __table_args__ = (
        Index('idx_card_batch_status', 'batch_id', 'status'),
        Index('idx_card_used_by', 'used_by'),
        Index('idx_card_expires_at', 'expires_at'),
    )


class CardUsageRecord(Base):
    """卡密使用记录表"""
    __tablename__ = "card_usage_records"

    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    card_id = Column(Integer, ForeignKey("cards.id", ondelete="CASCADE"), nullable=False, comment="卡密ID")
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False, comment="用户ID")
    
    # 权益信息
    benefit_type = Column(String(20), nullable=False, comment="权益类型")
    benefit_value = Column(Integer, nullable=False, comment="权益数值")
    benefit_unit = Column(String(20), nullable=False, comment="权益单位")
    
    # 使用环境信息
    ip_address = Column(String(45), nullable=True, comment="使用IP地址")
    user_agent = Column(Text, nullable=True, comment="用户代理信息")
    
    # 业务信息
    business_context = Column(Text, nullable=True, comment="业务上下文")
    
    used_at = Column(TIMESTAMP, server_default=func.current_timestamp(), comment="使用时间")

    # 关联关系
    card = relationship("Card", back_populates="usage_records")
    user = relationship("User")

    # 索引
    __table_args__ = (
        Index('idx_usage_user_benefit', 'user_id', 'benefit_type'),
        Index('idx_usage_card_user', 'card_id', 'user_id'),
        Index('idx_usage_used_at', 'used_at'),
    )


class UserBenefit(Base):
    """用户权益余额表"""
    __tablename__ = "user_benefits"

    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    user_id = Column(Integer, ForeignKey("users.id", ondelete="CASCADE"), nullable=False, comment="用户ID")
    
    # 权益信息
    benefit_type = Column(String(20), nullable=False, comment="权益类型")
    remaining_quota = Column(Integer, default=0, nullable=False, comment="剩余次数")
    
    # 有效期
    expires_at = Column(TIMESTAMP, nullable=True, comment="过期时间")
    
    # 来源信息
    source = Column(String(20), default="card", nullable=False, comment="来源：card/purchase")
    source_id = Column(Integer, nullable=True, comment="来源ID（卡密ID或订单ID）")
    
    # 状态
    is_active = Column(Boolean, default=True, nullable=False, comment="是否有效")
    
    created_at = Column(TIMESTAMP, server_default=func.current_timestamp(), comment="创建时间")
    updated_at = Column(
        TIMESTAMP,
        server_default=func.current_timestamp(),
        onupdate=func.current_timestamp(),
        comment="更新时间"
    )

    # 关联关系
    user = relationship("User")

    # 索引
    __table_args__ = (
        Index('idx_benefit_user_type', 'user_id', 'benefit_type'),
        Index('idx_benefit_expires_at', 'expires_at'),
        Index('idx_benefit_source', 'source', 'source_id'),
        Index('idx_benefit_active', 'is_active'),
    )
