"""
会员权益使用记录模型
专门用于记录和管理会员权益的消费情况
"""

from sqlalchemy import Column, Integer, String, Text, TIMESTAMP, Boolean, JSON, Index
from sqlalchemy.sql import func
from app.database import Base
import enum


class UsageStatus(enum.Enum):
    """使用状态枚举"""
    PENDING = "pending"      # 待确认
    CONFIRMED = "confirmed"  # 已确认
    CANCELLED = "cancelled"  # 已取消
    REFUNDED = "refunded"    # 已退还


class MembershipUsage(Base):
    """会员权益使用记录表"""
    __tablename__ = "membership_usage"

    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    user_id = Column(Integer, nullable=False, index=True, comment="用户ID")
    
    # 权益相关信息
    feature_name = Column(String(50), nullable=False, index=True, comment="功能名称，如resume_export, idphoto_generate")
    feature_display_name = Column(String(100), nullable=True, comment="功能显示名称")
    
    # 消费信息
    consumed_quota = Column(Integer, default=1, nullable=False, comment="消耗的配额数量")
    quota_type = Column(String(30), default="daily", comment="配额类型：daily, monthly, total")
    
    # 使用状态
    usage_status = Column(String(20), default="confirmed", comment="使用状态")
    
    # 关联信息
    related_action_id = Column(Integer, nullable=True, comment="关联的用户操作记录ID")
    resource_info = Column(JSON, nullable=True, comment="相关资源信息")
    
    # 业务信息
    business_context = Column(JSON, nullable=True, comment="业务上下文信息")
    usage_metadata = Column(JSON, nullable=True, comment="使用元数据")
    
    # 时间信息
    usage_date = Column(TIMESTAMP, nullable=False, server_default=func.current_timestamp(), comment="使用日期")
    confirmed_at = Column(TIMESTAMP, nullable=True, comment="确认时间")
    cancelled_at = Column(TIMESTAMP, nullable=True, comment="取消时间")
    
    # 审计信息
    created_at = Column(TIMESTAMP, server_default=func.current_timestamp(), comment="创建时间")
    updated_at = Column(
        TIMESTAMP,
        server_default=func.current_timestamp(),
        onupdate=func.current_timestamp(),
        comment="更新时间"
    )
    
    # 备注
    notes = Column(Text, nullable=True, comment="备注信息")

    # 复合索引优化查询性能
    __table_args__ = (
        Index('idx_user_feature_date', 'user_id', 'feature_name', 'usage_date'),
        Index('idx_feature_status', 'feature_name', 'usage_status'),
        Index('idx_usage_date', 'usage_date'),
        Index('idx_quota_type', 'quota_type'),
    )


class QuotaLimit(Base):
    """配额限制配置表"""
    __tablename__ = "quota_limits"

    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    feature_name = Column(String(50), nullable=False, index=True, comment="功能名称")
    
    # 配额配置
    quota_type = Column(String(30), nullable=False, comment="配额类型：daily, monthly, total")
    free_limit = Column(Integer, default=0, comment="免费用户限制")
    member_limit = Column(Integer, default=-1, comment="会员用户限制，-1表示无限制")
    
    # 配置信息
    is_active = Column(Boolean, default=True, comment="是否启用")
    priority = Column(Integer, default=0, comment="优先级，数值越大优先级越高")
    
    # 描述信息
    display_name = Column(String(100), nullable=True, comment="显示名称")
    description = Column(Text, nullable=True, comment="描述信息")
    
    # 时间信息
    effective_from = Column(TIMESTAMP, nullable=True, comment="生效开始时间")
    effective_until = Column(TIMESTAMP, nullable=True, comment="生效结束时间")
    
    created_at = Column(TIMESTAMP, server_default=func.current_timestamp(), comment="创建时间")
    updated_at = Column(
        TIMESTAMP,
        server_default=func.current_timestamp(),
        onupdate=func.current_timestamp(),
        comment="更新时间"
    )

    # 复合索引
    __table_args__ = (
        Index('idx_feature_quota_type', 'feature_name', 'quota_type'),
        Index('idx_active_priority', 'is_active', 'priority'),
    )
