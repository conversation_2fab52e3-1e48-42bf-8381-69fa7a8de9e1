"""
管理员管理工具
用于管理管理员用户、权限设置和API密钥管理
"""
import pymysql
import os
import logging
import secrets
import string
from datetime import datetime
from typing import Optional

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def get_database_connection():
    """获取数据库连接"""
    # 从环境变量读取数据库配置
    database_url = os.getenv("DATABASE_URL", "mysql+pymysql://resume_user:Resume123!@localhost/resume_service")
    
    # 解析数据库URL
    # 格式: mysql+pymysql://username:password@host/database
    if database_url.startswith("mysql+pymysql://"):
        url_parts = database_url.replace("mysql+pymysql://", "").split("/")
        db_name = url_parts[1]
        auth_host = url_parts[0].split("@")
        host = auth_host[1]
        user_pass = auth_host[0].split(":")
        user = user_pass[0]
        password = user_pass[1]
        
        return pymysql.connect(
            host=host,
            user=user,
            password=password,
            database=db_name,
            charset='utf8mb4'
        )
    else:
        raise ValueError("不支持的数据库URL格式")

def generate_api_key(length: int = 32) -> str:
    """生成安全的API密钥"""
    alphabet = string.ascii_letters + string.digits
    return ''.join(secrets.choice(alphabet) for _ in range(length))

def list_users():
    """列出所有用户及其管理员状态"""
    connection = None
    try:
        connection = get_database_connection()
        cursor = connection.cursor()
        
        query = """
        SELECT id, openid, nickname, is_member, is_admin, created_at 
        FROM users 
        ORDER BY created_at DESC
        """
        
        cursor.execute(query)
        users = cursor.fetchall()
        
        if not users:
            logger.info("没有找到任何用户")
            return
        
        logger.info("用户列表:")
        logger.info("-" * 100)
        logger.info(f"{'ID':<5} {'OpenID':<20} {'昵称':<15} {'会员状态':<10} {'管理员状态':<12} {'创建时间':<20}")
        logger.info("-" * 100)
        
        for user in users:
            user_id, openid, nickname, is_member, is_admin, created_at = user
            member_status = "会员" if is_member else "非会员"
            admin_status = "管理员" if is_admin else "普通用户"
            nickname = nickname or "未设置"
            logger.info(f"{user_id:<5} {openid:<20} {nickname:<15} {member_status:<10} {admin_status:<12} {created_at}")
        
        logger.info(f"\n总计: {len(users)} 个用户")
        
    except Exception as e:
        logger.error(f"列出用户失败: {str(e)}")
        
    finally:
        if connection:
            connection.close()

def stats():
    """显示用户统计信息"""
    connection = None
    try:
        connection = get_database_connection()
        cursor = connection.cursor()
        
        # 总用户数
        cursor.execute("SELECT COUNT(*) FROM users")
        total_users = cursor.fetchone()[0]
        
        # 会员用户数
        cursor.execute("SELECT COUNT(*) FROM users WHERE is_member = TRUE")
        member_users = cursor.fetchone()[0]
        
        # 管理员用户数
        cursor.execute("SELECT COUNT(*) FROM users WHERE is_admin = TRUE")
        admin_users = cursor.fetchone()[0]
        
        # 今日新增用户
        cursor.execute("SELECT COUNT(*) FROM users WHERE DATE(created_at) = CURDATE()")
        today_new_users = cursor.fetchone()[0]
        
        logger.info("用户统计信息:")
        logger.info("-" * 40)
        logger.info(f"总用户数: {total_users}")
        logger.info(f"会员用户数: {member_users}")
        logger.info(f"管理员用户数: {admin_users}")
        logger.info(f"今日新增用户: {today_new_users}")
        logger.info(f"会员比例: {(member_users/total_users*100):.1f}%" if total_users > 0 else "会员比例: 0%")
        logger.info(f"管理员比例: {(admin_users/total_users*100):.1f}%" if total_users > 0 else "管理员比例: 0%")
        
    except Exception as e:
        logger.error(f"获取统计信息失败: {str(e)}")
        
    finally:
        if connection:
            connection.close()

def search_user(keyword: str):
    """搜索用户"""
    if not keyword:
        logger.error("请提供搜索关键词")
        return
    
    connection = None
    try:
        connection = get_database_connection()
        cursor = connection.cursor()
        
        query = """
        SELECT id, openid, nickname, is_member, is_admin, created_at 
        FROM users 
        WHERE openid LIKE %s OR nickname LIKE %s
        ORDER BY created_at DESC
        """
        
        search_pattern = f"%{keyword}%"
        cursor.execute(query, (search_pattern, search_pattern))
        users = cursor.fetchall()
        
        if not users:
            logger.info(f"没有找到包含 '{keyword}' 的用户")
            return
        
        logger.info(f"搜索结果 (关键词: {keyword}):")
        logger.info("-" * 100)
        logger.info(f"{'ID':<5} {'OpenID':<20} {'昵称':<15} {'会员状态':<10} {'管理员状态':<12} {'创建时间':<20}")
        logger.info("-" * 100)
        
        for user in users:
            user_id, openid, nickname, is_member, is_admin, created_at = user
            member_status = "会员" if is_member else "非会员"
            admin_status = "管理员" if is_admin else "普通用户"
            nickname = nickname or "未设置"
            logger.info(f"{user_id:<5} {openid:<20} {nickname:<15} {member_status:<10} {admin_status:<12} {created_at}")
        
        logger.info(f"\n找到 {len(users)} 个匹配的用户")
        
    except Exception as e:
        logger.error(f"搜索用户失败: {str(e)}")
        
    finally:
        if connection:
            connection.close()

def set_admin(openid: str, is_admin: bool = True):
    """设置用户管理员权限"""
    if not openid:
        logger.error("请提供用户openid")
        return
    
    connection = None
    try:
        connection = get_database_connection()
        cursor = connection.cursor()
        
        # 检查用户是否存在
        cursor.execute("SELECT id, nickname, is_admin FROM users WHERE openid = %s", (openid,))
        user = cursor.fetchone()
        
        if not user:
            logger.error(f"未找到openid为 {openid} 的用户")
            return
        
        user_id, nickname, current_admin_status = user
        
        if current_admin_status == is_admin:
            status_text = "管理员" if is_admin else "普通用户"
            logger.info(f"用户 {openid} 已经是{status_text}，无需修改")
            return
        
        # 更新管理员状态
        cursor.execute(
            "UPDATE users SET is_admin = %s, updated_at = NOW() WHERE openid = %s",
            (is_admin, openid)
        )
        
        connection.commit()
        
        action = "设置为管理员" if is_admin else "取消管理员权限"
        logger.info(f"成功{action}: {openid} ({nickname or '未设置昵称'})")
        
    except Exception as e:
        logger.error(f"设置管理员权限失败: {str(e)}")
        if connection:
            connection.rollback()
        
    finally:
        if connection:
            connection.close()

def generate_new_api_key():
    """生成新的API密钥"""
    new_key = generate_api_key()
    logger.info("新的管理员API密钥:")
    logger.info("-" * 50)
    logger.info(f"API密钥: {new_key}")
    logger.info("-" * 50)
    logger.info("请将此密钥添加到.env文件中:")
    logger.info(f"ADMIN_API_KEY={new_key}")
    logger.info("")
    logger.info("⚠️  请妥善保管此密钥，它将用于管理员API访问")
    return new_key

def list_admins():
    """列出所有管理员用户"""
    connection = None
    try:
        connection = get_database_connection()
        cursor = connection.cursor()
        
        query = """
        SELECT id, openid, nickname, is_member, created_at 
        FROM users 
        WHERE is_admin = TRUE
        ORDER BY created_at DESC
        """
        
        cursor.execute(query)
        admins = cursor.fetchall()
        
        if not admins:
            logger.info("没有找到任何管理员用户")
            return
        
        logger.info("管理员用户列表:")
        logger.info("-" * 80)
        logger.info(f"{'ID':<5} {'OpenID':<20} {'昵称':<15} {'会员状态':<10} {'创建时间':<20}")
        logger.info("-" * 80)
        
        for admin in admins:
            user_id, openid, nickname, is_member, created_at = admin
            member_status = "会员" if is_member else "非会员"
            nickname = nickname or "未设置"
            logger.info(f"{user_id:<5} {openid:<20} {nickname:<15} {member_status:<10} {created_at}")
        
        logger.info(f"\n总计: {len(admins)} 个管理员")
        
    except Exception as e:
        logger.error(f"列出管理员失败: {str(e)}")
        
    finally:
        if connection:
            connection.close()

def main():
    """主函数"""
    import sys
    
    if len(sys.argv) < 2:
        print("管理员管理工具")
        print("用法: python admin_management.py <command> [args...]")
        print("")
        print("可用命令:")
        print("  list                    - 列出所有用户")
        print("  stats                   - 显示用户统计信息")
        print("  search <keyword>        - 搜索用户")
        print("  promote <openid>        - 设置用户为管理员")
        print("  demote <openid>         - 取消用户管理员权限")
        print("  admins                  - 列出所有管理员")
        print("  generate-key            - 生成新的API密钥")
        print("")
        print("示例:")
        print("  python admin_management.py list")
        print("  python admin_management.py promote oxxxxxxxxxxxxxx")
        print("  python admin_management.py search 张三")
        return
    
    command = sys.argv[1]
    
    if command == "list":
        list_users()
    elif command == "stats":
        stats()
    elif command == "search":
        if len(sys.argv) < 3:
            print("❌ 请提供搜索关键词")
            return
        search_user(sys.argv[2])
    elif command == "promote":
        if len(sys.argv) < 3:
            print("❌ 请提供用户openid")
            return
        set_admin(sys.argv[2], True)
    elif command == "demote":
        if len(sys.argv) < 3:
            print("❌ 请提供用户openid")
            return
        set_admin(sys.argv[2], False)
    elif command == "admins":
        list_admins()
    elif command == "generate-key":
        generate_new_api_key()
    else:
        print(f"❌ 未知命令: {command}")
        print("使用 'python admin_management.py' 查看帮助")

if __name__ == "__main__":
    main()
