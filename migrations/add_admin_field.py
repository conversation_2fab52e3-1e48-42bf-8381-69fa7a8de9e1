"""
添加用户管理员字段的数据库迁移脚本
"""
import pymysql
import os
import logging
from datetime import datetime

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def get_database_connection():
    """获取数据库连接"""
    # 从环境变量读取数据库配置
    database_url = os.getenv("DATABASE_URL", "mysql+pymysql://resume_user:Resume123!@localhost/resume_service")
    
    # 解析数据库URL
    # 格式: mysql+pymysql://username:password@host/database
    if database_url.startswith("mysql+pymysql://"):
        url_parts = database_url.replace("mysql+pymysql://", "").split("/")
        db_name = url_parts[1]
        auth_host = url_parts[0].split("@")
        host = auth_host[1]
        user_pass = auth_host[0].split(":")
        user = user_pass[0]
        password = user_pass[1]
        
        return pymysql.connect(
            host=host,
            user=user,
            password=password,
            database=db_name,
            charset='utf8mb4'
        )
    else:
        raise ValueError("不支持的数据库URL格式")

def add_admin_field():
    """添加is_admin字段到users表"""
    connection = None
    try:
        connection = get_database_connection()
        cursor = connection.cursor()
        
        # 检查字段是否已存在
        cursor.execute("""
            SELECT COUNT(*) 
            FROM INFORMATION_SCHEMA.COLUMNS 
            WHERE TABLE_SCHEMA = DATABASE() 
            AND TABLE_NAME = 'users' 
            AND COLUMN_NAME = 'is_admin'
        """)
        
        field_exists = cursor.fetchone()[0] > 0
        
        if field_exists:
            logger.info("is_admin字段已存在，跳过添加")
            return True
        
        # 添加is_admin字段
        logger.info("开始添加is_admin字段...")
        cursor.execute("""
            ALTER TABLE users 
            ADD COLUMN is_admin BOOLEAN NOT NULL DEFAULT FALSE 
            COMMENT '是否管理员' 
            AFTER is_member
        """)
        
        connection.commit()
        logger.info("成功添加is_admin字段")
        
        # 如果配置了默认管理员openid，则设置为管理员
        default_admin_openid = os.getenv("DEFAULT_ADMIN_OPENID", "")
        if default_admin_openid:
            cursor.execute("""
                UPDATE users 
                SET is_admin = TRUE 
                WHERE openid = %s
            """, (default_admin_openid,))
            
            affected_rows = cursor.rowcount
            connection.commit()
            
            if affected_rows > 0:
                logger.info(f"已将用户 {default_admin_openid} 设置为管理员")
            else:
                logger.warning(f"未找到openid为 {default_admin_openid} 的用户")
        
        return True
        
    except Exception as e:
        logger.error(f"添加is_admin字段失败: {str(e)}")
        if connection:
            connection.rollback()
        return False
        
    finally:
        if connection:
            connection.close()

def rollback_admin_field():
    """回滚：删除is_admin字段"""
    connection = None
    try:
        connection = get_database_connection()
        cursor = connection.cursor()
        
        # 检查字段是否存在
        cursor.execute("""
            SELECT COUNT(*) 
            FROM INFORMATION_SCHEMA.COLUMNS 
            WHERE TABLE_SCHEMA = DATABASE() 
            AND TABLE_NAME = 'users' 
            AND COLUMN_NAME = 'is_admin'
        """)
        
        field_exists = cursor.fetchone()[0] > 0
        
        if not field_exists:
            logger.info("is_admin字段不存在，无需删除")
            return True
        
        # 删除is_admin字段
        logger.info("开始删除is_admin字段...")
        cursor.execute("ALTER TABLE users DROP COLUMN is_admin")
        
        connection.commit()
        logger.info("成功删除is_admin字段")
        return True
        
    except Exception as e:
        logger.error(f"删除is_admin字段失败: {str(e)}")
        if connection:
            connection.rollback()
        return False
        
    finally:
        if connection:
            connection.close()

def main():
    """主函数"""
    import sys
    
    if len(sys.argv) < 2:
        print("用法: python add_admin_field.py [migrate|rollback]")
        print("  migrate  - 添加is_admin字段")
        print("  rollback - 删除is_admin字段")
        return
    
    action = sys.argv[1]
    
    if action == "migrate":
        success = add_admin_field()
        if success:
            print("✅ 迁移成功：已添加is_admin字段")
        else:
            print("❌ 迁移失败")
            sys.exit(1)
            
    elif action == "rollback":
        success = rollback_admin_field()
        if success:
            print("✅ 回滚成功：已删除is_admin字段")
        else:
            print("❌ 回滚失败")
            sys.exit(1)
            
    else:
        print("❌ 无效的操作，请使用 migrate 或 rollback")
        sys.exit(1)

if __name__ == "__main__":
    main()
