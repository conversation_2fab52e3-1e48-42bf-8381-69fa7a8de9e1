#!/usr/bin/env python3
"""
会员权益消费模块分离迁移脚本
1. 创建新的membership_usage和quota_limits表
2. 清理user_actions表中的会员权益相关字段
3. 数据迁移（如果需要）
"""

import pymysql
import logging
import sys
import os
from datetime import datetime
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 从DATABASE_URL解析数据库配置
DATABASE_URL = os.getenv("DATABASE_URL", "mysql+pymysql://resume_user:Resume123!@localhost/resume_service")

import re
url_pattern = r'mysql\+pymysql://([^:]+):([^@]+)@([^:/]+)(?::(\d+))?/(.+)'
match = re.match(url_pattern, DATABASE_URL)

if match:
    username, password, host, port, database = match.groups()
    DB_CONFIG = {
        'host': host,
        'port': int(port) if port else 3306,
        'user': username,
        'password': password,
        'database': database,
        'charset': 'utf8mb4'
    }
else:
    # 默认配置
    DB_CONFIG = {
        'host': 'localhost',
        'port': 3306,
        'user': 'resume_user',
        'password': 'Resume123!',
        'database': 'resume_service',
        'charset': 'utf8mb4'
    }

def get_connection():
    """获取数据库连接"""
    try:
        connection = pymysql.connect(**DB_CONFIG)
        logger.info("数据库连接成功")
        return connection
    except Exception as e:
        logger.error(f"数据库连接失败: {e}")
        return None

def check_table_exists(connection, table_name):
    """检查表是否存在"""
    try:
        cursor = connection.cursor()
        cursor.execute(f"""
            SELECT COUNT(*) 
            FROM INFORMATION_SCHEMA.TABLES 
            WHERE TABLE_SCHEMA = '{DB_CONFIG['database']}' 
            AND TABLE_NAME = '{table_name}'
        """)
        result = cursor.fetchone()
        return result[0] > 0
    except Exception as e:
        logger.error(f"检查表是否存在失败: {e}")
        return False

def create_membership_usage_table(connection):
    """创建会员权益使用记录表"""
    try:
        cursor = connection.cursor()
        
        create_table_sql = """
        CREATE TABLE membership_usage (
            id INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
            user_id INT NOT NULL COMMENT '用户ID',
            
            -- 权益相关信息
            feature_name VARCHAR(50) NOT NULL COMMENT '功能名称，如resume_export, idphoto_generate',
            feature_display_name VARCHAR(100) NULL COMMENT '功能显示名称',
            
            -- 消费信息
            consumed_quota INT DEFAULT 1 NOT NULL COMMENT '消耗的配额数量',
            quota_type VARCHAR(30) DEFAULT 'daily' COMMENT '配额类型：daily, monthly, total',
            
            -- 使用状态
            usage_status VARCHAR(20) DEFAULT 'confirmed' COMMENT '使用状态',
            
            -- 关联信息
            related_action_id INT NULL COMMENT '关联的用户操作记录ID',
            resource_info JSON NULL COMMENT '相关资源信息',
            
            -- 业务信息
            business_context JSON NULL COMMENT '业务上下文信息',
            usage_metadata JSON NULL COMMENT '使用元数据',
            
            -- 时间信息
            usage_date TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '使用日期',
            confirmed_at TIMESTAMP NULL COMMENT '确认时间',
            cancelled_at TIMESTAMP NULL COMMENT '取消时间',
            
            -- 审计信息
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
            
            -- 备注
            notes TEXT NULL COMMENT '备注信息',
            
            -- 索引
            INDEX idx_user_id (user_id),
            INDEX idx_feature_name (feature_name),
            INDEX idx_user_feature_date (user_id, feature_name, usage_date),
            INDEX idx_feature_status (feature_name, usage_status),
            INDEX idx_usage_date (usage_date),
            INDEX idx_quota_type (quota_type)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='会员权益使用记录表';
        """
        
        cursor.execute(create_table_sql)
        logger.info("成功创建 membership_usage 表")
        return True
        
    except Exception as e:
        logger.error(f"创建 membership_usage 表失败: {e}")
        return False

def create_quota_limits_table(connection):
    """创建配额限制配置表"""
    try:
        cursor = connection.cursor()
        
        create_table_sql = """
        CREATE TABLE quota_limits (
            id INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
            feature_name VARCHAR(50) NOT NULL COMMENT '功能名称',
            
            -- 配额配置
            quota_type VARCHAR(30) NOT NULL COMMENT '配额类型：daily, monthly, total',
            free_limit INT DEFAULT 0 COMMENT '免费用户限制',
            member_limit INT DEFAULT -1 COMMENT '会员用户限制，-1表示无限制',
            
            -- 配置信息
            is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
            priority INT DEFAULT 0 COMMENT '优先级，数值越大优先级越高',
            
            -- 描述信息
            display_name VARCHAR(100) NULL COMMENT '显示名称',
            description TEXT NULL COMMENT '描述信息',
            
            -- 时间信息
            effective_from TIMESTAMP NULL COMMENT '生效开始时间',
            effective_until TIMESTAMP NULL COMMENT '生效结束时间',
            
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
            
            -- 索引
            INDEX idx_feature_name (feature_name),
            INDEX idx_feature_quota_type (feature_name, quota_type),
            INDEX idx_active_priority (is_active, priority)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='配额限制配置表';
        """
        
        cursor.execute(create_table_sql)
        logger.info("成功创建 quota_limits 表")
        return True
        
    except Exception as e:
        logger.error(f"创建 quota_limits 表失败: {e}")
        return False

def insert_default_quota_configs(connection):
    """插入默认配额配置"""
    try:
        cursor = connection.cursor()
        
        default_configs = [
            ('resume_export', 'daily', '简历导出', 3, 20),
            ('idphoto_generate', 'daily', '证件照生成', 2, 10),
            ('premium_templates', 'total', '高级模板', 0, -1),
            ('watermark_free', 'total', '无水印导出', 0, -1),
        ]
        
        for feature_name, quota_type, display_name, free_limit, member_limit in default_configs:
            insert_sql = """
            INSERT INTO quota_limits 
            (feature_name, quota_type, display_name, free_limit, member_limit, is_active, priority)
            VALUES (%s, %s, %s, %s, %s, TRUE, 1)
            """
            cursor.execute(insert_sql, (feature_name, quota_type, display_name, free_limit, member_limit))
        
        logger.info("成功插入默认配额配置")
        return True
        
    except Exception as e:
        logger.error(f"插入默认配额配置失败: {e}")
        return False

def cleanup_user_actions_table(connection):
    """清理user_actions表中的会员权益相关字段"""
    try:
        cursor = connection.cursor()
        
        # 要删除的字段列表
        fields_to_remove = [
            'feature_name',
            'is_member_action', 
            'consumed_quota'
        ]
        
        removed_fields = []
        for field in fields_to_remove:
            try:
                # 检查字段是否存在
                cursor.execute(f"""
                    SELECT COUNT(*) 
                    FROM INFORMATION_SCHEMA.COLUMNS 
                    WHERE TABLE_SCHEMA = '{DB_CONFIG['database']}' 
                    AND TABLE_NAME = 'user_actions' 
                    AND COLUMN_NAME = '{field}'
                """)
                
                if cursor.fetchone()[0] > 0:
                    cursor.execute(f"ALTER TABLE user_actions DROP COLUMN {field}")
                    removed_fields.append(field)
                    logger.info(f"成功删除字段: {field}")
                else:
                    logger.info(f"字段 {field} 不存在，跳过删除")
            except Exception as e:
                logger.warning(f"删除字段 {field} 失败: {e}")
        
        # 添加user_agent字段（如果不存在）
        try:
            cursor.execute(f"""
                SELECT COUNT(*) 
                FROM INFORMATION_SCHEMA.COLUMNS 
                WHERE TABLE_SCHEMA = '{DB_CONFIG['database']}' 
                AND TABLE_NAME = 'user_actions' 
                AND COLUMN_NAME = 'user_agent'
            """)
            
            if cursor.fetchone()[0] == 0:
                cursor.execute("ALTER TABLE user_actions ADD COLUMN user_agent TEXT NULL COMMENT '用户代理信息'")
                logger.info("成功添加 user_agent 字段")
        except Exception as e:
            logger.warning(f"添加 user_agent 字段失败: {e}")
        
        logger.info(f"user_actions表清理完成，删除了 {len(removed_fields)} 个字段")
        return True
        
    except Exception as e:
        logger.error(f"清理user_actions表失败: {e}")
        return False

def main():
    """主函数"""
    logger.info("开始会员权益消费模块分离迁移...")
    
    # 获取数据库连接
    connection = get_connection()
    if not connection:
        logger.error("无法连接到数据库，退出")
        sys.exit(1)
    
    try:
        # 1. 创建新表
        if not check_table_exists(connection, 'membership_usage'):
            if not create_membership_usage_table(connection):
                logger.error("创建membership_usage表失败")
                sys.exit(1)
        else:
            logger.info("membership_usage表已存在，跳过创建")
        
        if not check_table_exists(connection, 'quota_limits'):
            if not create_quota_limits_table(connection):
                logger.error("创建quota_limits表失败")
                sys.exit(1)
            
            # 插入默认配置
            if not insert_default_quota_configs(connection):
                logger.error("插入默认配额配置失败")
                sys.exit(1)
        else:
            logger.info("quota_limits表已存在，跳过创建")
        
        # 2. 清理user_actions表
        if not cleanup_user_actions_table(connection):
            logger.error("清理user_actions表失败")
            sys.exit(1)
        
        # 提交所有更改
        connection.commit()
        logger.info("会员权益消费模块分离迁移完成")
        
    except Exception as e:
        logger.error(f"迁移过程中发生异常: {e}")
        connection.rollback()
        sys.exit(1)
    finally:
        connection.close()
        logger.info("数据库连接已关闭")

if __name__ == "__main__":
    main()
