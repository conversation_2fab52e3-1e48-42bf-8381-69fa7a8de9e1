# 微信支付功能设计文档

## 概述

本设计文档详细描述了为简历服务微信小程序添加支付功能的技术方案。该功能将实现会员购买、权益管理、订单处理等核心功能，并与现有系统无缝集成。设计遵循模块化、可扩展、安全可靠的原则，确保系统能够满足当前需求并适应未来的功能扩展。

## 架构

### 整体架构

微信支付功能将采用分层架构，主要包括以下几层：

1. **API层**：提供RESTful API接口，处理来自客户端的请求
2. **服务层**：实现业务逻辑，处理支付流程、订单管理、会员权益等
3. **数据访问层**：负责与数据库交互，提供数据持久化服务
4. **集成层**：与微信支付API和其他外部系统集成

系统架构图如下：

```mermaid
graph TD
    Client[微信小程序客户端] --> API[API层]
    API --> Service[服务层]
    Service --> DAL[数据访问层]
    Service --> Integration[集成层]
    Integration --> WechatPay[微信支付平台]
    DAL --> Database[(数据库)]
    Admin[管理后台] --> API
```

### 模块划分

系统将划分为以下主要模块：

1. **支付核心模块**：处理与微信支付平台的交互，包括统一下单、查询订单、接收通知等
2. **订单管理模块**：处理订单的创建、查询、状态更新等
3. **会员权益模块**：管理用户的会员状态、权益消耗和权益更新
4. **卡密系统模块**：处理卡密的生成、验证和兑换
5. **用户行为记录模块**：扩展现有模块，记录用户的关键操作
6. **多商户管理模块**：支持多个商户和微信小程序的配置和管理
7. **管理后台模块**：提供管理员操作界面和API

## 组件和接口

### API接口设计

#### 支付相关接口

1. **创建支付订单**
   - 路径: `/api/payment/create-order`
   - 方法: POST
   - 请求参数:
     ```json
     {
       "product_id": "string",
       "product_type": "member_time|member_count",
       "amount": "number",
       "description": "string",
       "source": "string"
     }
     ```
   - 响应:
     ```json
     {
       "order_id": "string",
       "payment_params": {
         "appId": "string",
         "timeStamp": "string",
         "nonceStr": "string",
         "package": "string",
         "signType": "string",
         "paySign": "string"
       }
     }
     ```

2. **查询订单状态**
   - 路径: `/api/payment/query-order/{order_id}`
   - 方法: GET
   - 响应:
     ```json
     {
       "order_id": "string",
       "status": "pending|paid|expired|refunded",
       "created_at": "datetime",
       "paid_at": "datetime|null",
       "amount": "number",
       "description": "string"
     }
     ```

3. **支付结果通知**
   - 路径: `/api/payment/notify`
   - 方法: POST
   - 请求: 微信支付平台的XML或JSON通知
   - 响应: 成功接收的确认信息

#### 会员权益相关接口

1. **查询会员权益**
   - 路径: `/api/member/benefits`
   - 方法: GET
   - 响应:
     ```json
     {
       "is_member": "boolean",
       "member_type": "string|null",
       "expire_at": "datetime|null",
       "benefits": {
         "pdf_download_count": "number",
         "photo_download_count": "number",
         "premium_templates_access": "boolean"
       }
     }
     ```

2. **消耗权益**
   - 路径: `/api/member/consume-benefit`
   - 方法: POST
   - 请求参数:
     ```json
     {
       "benefit_type": "pdf_download|photo_download|template_access",
       "resource_id": "string"
     }
     ```
   - 响应:
     ```json
     {
       "success": "boolean",
       "remaining": "number",
       "message": "string"
     }
     ```

#### 卡密相关接口

1. **兑换卡密**
   - 路径: `/api/card/redeem`
   - 方法: POST
   - 请求参数:
     ```json
     {
       "card_code": "string"
     }
     ```
   - 响应:
     ```json
     {
       "success": "boolean",
       "benefit_type": "member_time|pdf_count|photo_count",
       "benefit_value": "number",
       "message": "string"
     }
     ```

2. **查询卡密状态**
   - 路径: `/api/card/check`
   - 方法: POST
   - 请求参数:
     ```json
     {
       "card_code": "string"
     }
     ```
   - 响应:
     ```json
     {
       "valid": "boolean",
       "used": "boolean",
       "benefit_type": "string",
       "benefit_value": "number"
     }
     ```

#### 管理员接口

1. **生成卡密**
   - 路径: `/api/admin/card/generate`
   - 方法: POST
   - 请求参数:
     ```json
     {
       "count": "number",
       "benefit_type": "member_time|pdf_count|photo_count",
       "benefit_value": "number",
       "batch_name": "string"
     }
     ```
   - 响应:
     ```json
     {
       "success": "boolean",
       "cards": ["string"],
       "batch_id": "string"
     }
     ```

2. **订单统计**
   - 路径: `/api/admin/payment/stats`
   - 方法: GET
   - 请求参数: 时间范围、商户ID等筛选条件
   - 响应: 订单统计数据

### 数据模型

#### 新增数据表

1. **商户表 (merchants)**
   ```sql
   CREATE TABLE merchants (
     id INT AUTO_INCREMENT PRIMARY KEY,
     merchant_name VARCHAR(100) NOT NULL,
     merchant_code VARCHAR(50) NOT NULL UNIQUE,
     app_id VARCHAR(100) NOT NULL,
     app_secret VARCHAR(255) NOT NULL,
     mch_id VARCHAR(50) NOT NULL,
     mch_key VARCHAR(255) NOT NULL,
     cert_path VARCHAR(255),
     key_path VARCHAR(255),
     notify_url VARCHAR(255) NOT NULL,
     created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
     updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
     status ENUM('active', 'inactive') DEFAULT 'active',
     INDEX idx_merchant_code (merchant_code)
   );
   ```

2. **订单表 (orders)**
   ```sql
   CREATE TABLE orders (
     id INT AUTO_INCREMENT PRIMARY KEY,
     order_no VARCHAR(50) NOT NULL UNIQUE,
     user_id INT NOT NULL,
     merchant_id INT NOT NULL,
     product_id VARCHAR(50) NOT NULL,
     product_type VARCHAR(50) NOT NULL,
     amount DECIMAL(10, 2) NOT NULL,
     description VARCHAR(255),
     status ENUM('pending', 'paid', 'expired', 'refunded') DEFAULT 'pending',
     transaction_id VARCHAR(100),
     created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
     paid_at TIMESTAMP NULL,
     expired_at TIMESTAMP NULL,
     refunded_at TIMESTAMP NULL,
     notify_data TEXT,
     client_ip VARCHAR(45),
     source VARCHAR(50),
     INDEX idx_user_id (user_id),
     INDEX idx_order_no (order_no),
     INDEX idx_merchant_id (merchant_id),
     INDEX idx_status_created (status, created_at)
   );
   ```

3. **会员权益表 (member_benefits)**
   ```sql
   CREATE TABLE member_benefits (
     id INT AUTO_INCREMENT PRIMARY KEY,
     user_id INT NOT NULL,
     benefit_type VARCHAR(50) NOT NULL,
     benefit_value INT NOT NULL,
     start_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
     expire_at TIMESTAMP NULL,
     created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
     updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
     order_id INT,
     card_id INT,
     status ENUM('active', 'expired', 'consumed') DEFAULT 'active',
     INDEX idx_user_id (user_id),
     INDEX idx_type_status (benefit_type, status),
     INDEX idx_expire_at (expire_at)
   );
   ```

4. **权益消耗记录表 (benefit_consumption)**
   ```sql
   CREATE TABLE benefit_consumption (
     id INT AUTO_INCREMENT PRIMARY KEY,
     user_id INT NOT NULL,
     benefit_id INT NOT NULL,
     benefit_type VARCHAR(50) NOT NULL,
     consumed_value INT NOT NULL,
     resource_id VARCHAR(100),
     created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
     status ENUM('success', 'failed', 'refunded') DEFAULT 'success',
     remark VARCHAR(255),
     INDEX idx_user_id (user_id),
     INDEX idx_benefit_id (benefit_id),
     INDEX idx_created_at (created_at)
   );
   ```

5. **卡密表 (card_codes)**
   ```sql
   CREATE TABLE card_codes (
     id INT AUTO_INCREMENT PRIMARY KEY,
     code VARCHAR(50) NOT NULL UNIQUE,
     batch_id VARCHAR(50) NOT NULL,
     benefit_type VARCHAR(50) NOT NULL,
     benefit_value INT NOT NULL,
     status ENUM('unused', 'used', 'invalid') DEFAULT 'unused',
     created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
     used_at TIMESTAMP NULL,
     used_by INT NULL,
     created_by INT NOT NULL,
     expire_at TIMESTAMP NULL,
     INDEX idx_code (code),
     INDEX idx_batch_id (batch_id),
     INDEX idx_status (status)
   );
   ```

6. **卡密批次表 (card_batches)**
   ```sql
   CREATE TABLE card_batches (
     id INT AUTO_INCREMENT PRIMARY KEY,
     batch_id VARCHAR(50) NOT NULL UNIQUE,
     batch_name VARCHAR(100) NOT NULL,
     benefit_type VARCHAR(50) NOT NULL,
     benefit_value INT NOT NULL,
     total_count INT NOT NULL,
     used_count INT DEFAULT 0,
     created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
     created_by INT NOT NULL,
     expire_at TIMESTAMP NULL,
     status ENUM('active', 'expired', 'disabled') DEFAULT 'active',
     INDEX idx_batch_id (batch_id),
     INDEX idx_status (status)
   );
   ```

7. **管理员表 (administrators)**
   ```sql
   CREATE TABLE administrators (
     id INT AUTO_INCREMENT PRIMARY KEY,
     username VARCHAR(50) NOT NULL UNIQUE,
     password_hash VARCHAR(255) NOT NULL,
     real_name VARCHAR(100),
     email VARCHAR(100),
     role ENUM('super_admin', 'admin', 'operator') DEFAULT 'operator',
     last_login_at TIMESTAMP NULL,
     created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
     updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
     status ENUM('active', 'inactive') DEFAULT 'active',
     INDEX idx_username (username),
     INDEX idx_role (role)
   );
   ```

#### 修改现有表

1. **用户表 (users)**
   ```sql
   ALTER TABLE users
   ADD member_expire_at TIMESTAMP NULL AFTER is_member,
   ADD member_level VARCHAR(50) DEFAULT 'standard' AFTER is_member,
   ADD merchant_id INT NULL,
   ADD INDEX idx_member_expire (member_expire_at),
   ADD INDEX idx_member_level (member_level),
   ADD INDEX idx_merchant_id (merchant_id);
   ```

2. **用户行为表 (user_actions)**
   ```sql
   ALTER TABLE user_actions
   ADD benefit_id INT NULL,
   ADD resource_id VARCHAR(100) NULL,
   ADD merchant_id INT NULL,
   ADD INDEX idx_benefit_id (benefit_id),
   ADD INDEX idx_merchant_id (merchant_id);
   ```

### 服务组件设计

#### 支付服务 (PaymentService)

负责处理支付相关的核心业务逻辑，包括：

1. 创建支付订单
2. 生成微信支付参数
3. 处理支付结果通知
4. 查询订单状态
5. 处理退款请求（后端扩展功能）

```python
class PaymentService:
    def __init__(self, merchant_service, order_repository, benefit_service):
        self.merchant_service = merchant_service
        self.order_repository = order_repository
        self.benefit_service = benefit_service
        
    async def create_order(self, user_id, product_id, product_type, amount, description, source):
        # 实现创建订单逻辑
        pass
        
    async def generate_payment_params(self, order_id, client_ip):
        # 实现生成微信支付参数逻辑
        pass
        
    async def process_payment_notification(self, notification_data):
        # 实现处理支付通知逻辑
        pass
        
    async def query_order_status(self, order_id):
        # 实现查询订单状态逻辑
        pass
        
    async def request_refund(self, order_id, refund_amount, refund_reason):
        # 实现退款请求逻辑（后端扩展功能）
        pass
```

#### 会员权益服务 (MemberBenefitService)

负责管理用户的会员权益，包括：

1. 激活会员权益
2. 查询用户权益
3. 消耗权益
4. 检查权益是否充足
5. 处理权益过期

```python
class MemberBenefitService:
    def __init__(self, benefit_repository, user_repository, action_service):
        self.benefit_repository = benefit_repository
        self.user_repository = user_repository
        self.action_service = action_service
        
    async def activate_time_benefit(self, user_id, days, source_type, source_id):
        # 实现激活时长权益逻辑
        pass
        
    async def activate_count_benefit(self, user_id, benefit_type, count, source_type, source_id):
        # 实现激活次数权益逻辑
        pass
        
    async def get_user_benefits(self, user_id):
        # 实现查询用户权益逻辑
        pass
        
    async def consume_benefit(self, user_id, benefit_type, resource_id=None):
        # 实现消耗权益逻辑
        pass
        
    async def check_benefit_availability(self, user_id, benefit_type):
        # 实现检查权益是否充足逻辑
        pass
        
    async def process_expired_benefits(self):
        # 实现处理过期权益逻辑
        pass
```

#### 卡密服务 (CardCodeService)

负责管理卡密的生成、验证和兑换，包括：

1. 生成卡密
2. 验证卡密有效性
3. 兑换卡密
4. 查询卡密状态
5. 管理卡密批次

```python
class CardCodeService:
    def __init__(self, card_repository, batch_repository, benefit_service):
        self.card_repository = card_repository
        self.batch_repository = batch_repository
        self.benefit_service = benefit_service
        
    async def generate_cards(self, count, benefit_type, benefit_value, batch_name, admin_id):
        # 实现生成卡密逻辑
        pass
        
    async def validate_card(self, card_code):
        # 实现验证卡密有效性逻辑
        pass
        
    async def redeem_card(self, user_id, card_code):
        # 实现兑换卡密逻辑
        pass
        
    async def get_card_status(self, card_code):
        # 实现查询卡密状态逻辑
        pass
        
    async def get_batch_statistics(self, batch_id=None):
        # 实现查询批次统计逻辑
        pass
```

#### 商户服务 (MerchantService)

负责管理多商户配置和微信支付参数，包括：

1. 获取商户配置
2. 验证商户API密钥
3. 管理商户证书
4. 处理多商户请求路由

```python
class MerchantService:
    def __init__(self, merchant_repository, config_service):
        self.merchant_repository = merchant_repository
        self.config_service = config_service
        
    async def get_merchant_by_code(self, merchant_code):
        # 实现获取商户配置逻辑
        pass
        
    async def get_merchant_by_app_id(self, app_id):
        # 实现通过AppID获取商户逻辑
        pass
        
    async def validate_merchant_api_key(self, merchant_id, api_key):
        # 实现验证商户API密钥逻辑
        pass
        
    async def get_payment_config(self, merchant_id):
        # 实现获取支付配置逻辑
        pass
        
    async def update_merchant_config(self, merchant_id, config_data):
        # 实现更新商户配置逻辑
        pass
```

#### 微信支付客户端 (WechatPayClient)

负责与微信支付API交互，包括：

1. 统一下单
2. 查询订单
3. 申请退款
4. 验证支付通知签名
5. 生成签名

```python
class WechatPayClient:
    def __init__(self, merchant_service):
        self.merchant_service = merchant_service
        
    async def unified_order(self, merchant_id, order_data):
        # 实现统一下单逻辑
        pass
        
    async def query_order(self, merchant_id, transaction_id=None, out_trade_no=None):
        # 实现查询订单逻辑
        pass
        
    async def apply_refund(self, merchant_id, refund_data):
        # 实现申请退款逻辑
        pass
        
    async def verify_notification(self, merchant_id, notification_data):
        # 实现验证支付通知签名逻辑
        pass
        
    def generate_sign(self, merchant_id, data):
        # 实现生成签名逻辑
        pass
```

#### 用户行为记录服务 (UserActionService)

扩展现有的用户行为记录功能，包括：

1. 记录用户行为
2. 查询用户行为历史
3. 生成行为统计报告

```python
class UserActionService:
    def __init__(self, action_repository, user_repository):
        self.action_repository = action_repository
        self.user_repository = user_repository
        
    async def record_action(self, user_id, action_type, action_content=None, template_id=None, resource_id=None, benefit_id=None, merchant_id=None):
        # 实现记录用户行为逻辑
        pass
        
    async def get_user_actions(self, user_id, action_type=None, start_date=None, end_date=None):
        # 实现查询用户行为历史逻辑
        pass
        
    async def generate_action_statistics(self, action_type=None, start_date=None, end_date=None, merchant_id=None):
        # 实现生成行为统计报告逻辑
        pass
```

## 数据流

### 支付流程

```mermaid
sequenceDiagram
    participant Client as 微信小程序
    participant API as API层
    participant PaymentService as 支付服务
    participant MerchantService as 商户服务
    participant WechatPay as 微信支付API
    participant OrderRepo as 订单仓库
    participant BenefitService as 权益服务
    
    Client->>API: 1. 创建订单请求
    API->>PaymentService: 2. 创建订单
    PaymentService->>MerchantService: 3. 获取商户配置
    MerchantService-->>PaymentService: 4. 返回商户配置
    PaymentService->>OrderRepo: 5. 保存订单
    OrderRepo-->>PaymentService: 6. 订单ID
    PaymentService->>WechatPay: 7. 统一下单
    WechatPay-->>PaymentService: 8. 预支付ID
    PaymentService->>API: 9. 返回支付参数
    API->>Client: 10. 返回支付参数
    
    Client->>WechatPay: 11. 用户确认支付
    WechatPay->>API: 12. 支付结果通知
    API->>PaymentService: 13. 处理支付通知
    PaymentService->>OrderRepo: 14. 更新订单状态
    PaymentService->>BenefitService: 15. 激活会员权益
    BenefitService-->>PaymentService: 16. 权益激活结果
    PaymentService-->>API: 17. 通知处理结果
    API-->>WechatPay: 18. 返回成功接收
    
    Client->>API: 19. 查询订单状态
    API->>PaymentService: 20. 查询订单
    PaymentService->>OrderRepo: 21. 获取订单信息
    OrderRepo-->>PaymentService: 22. 订单数据
    PaymentService->>API: 23. 返回订单状态
    API->>Client: 24. 显示订单状态
```

### 卡密兑换流程

```mermaid
sequenceDiagram
    participant Client as 微信小程序
    participant API as API层
    participant CardService as 卡密服务
    participant CardRepo as 卡密仓库
    participant BenefitService as 权益服务
    participant UserRepo as 用户仓库
    
    Client->>API: 1. 提交卡密兑换请求
    API->>CardService: 2. 验证卡密
    CardService->>CardRepo: 3. 查询卡密信息
    CardRepo-->>CardService: 4. 卡密数据
    
    alt 卡密有效
        CardService->>CardRepo: 5. 标记卡密为已使用
        CardService->>BenefitService: 6. 激活对应权益
        BenefitService->>UserRepo: 7. 更新用户权益
        UserRepo-->>BenefitService: 8. 更新成功
        BenefitService-->>CardService: 9. 权益激活成功
        CardService->>API: 10. 返回兑换成功
        API->>Client: 11. 显示兑换成功
    else 卡密无效
        CardService->>API: 5. 返回错误信息
        API->>Client: 6. 显示错误信息
    end
```

### 权益消耗流程

```mermaid
sequenceDiagram
    participant Client as 微信小程序
    participant API as API层
    participant ResourceService as 资源服务
    participant BenefitService as 权益服务
    participant BenefitRepo as 权益仓库
    participant ActionService as 行为记录服务
    
    Client->>API: 1. 请求使用资源
    API->>BenefitService: 2. 检查权益是否充足
    BenefitService->>BenefitRepo: 3. 查询用户权益
    BenefitRepo-->>BenefitService: 4. 权益数据
    
    alt 权益充足
        BenefitService-->>API: 5. 权益验证通过
        API->>ResourceService: 6. 处理资源请求
        ResourceService-->>API: 7. 资源处理结果
        
        alt 资源处理成功
            API->>BenefitService: 8. 消耗权益
            BenefitService->>BenefitRepo: 9. 更新权益记录
            BenefitService->>ActionService: 10. 记录权益使用
            BenefitService-->>API: 11. 权益消耗成功
            API->>Client: 12. 返回资源和剩余权益
        else 资源处理失败
            API->>Client: 8. 返回错误信息
        end
    else 权益不足
        BenefitService-->>API: 5. 权益不足
        API->>Client: 6. 提示购买或兑换权益
    end
```

## 安全设计

### 数据安全

1. **敏感数据加密**
   - 商户密钥、证书等敏感信息使用强加密算法存储
   - 数据库密码和敏感配置使用环境变量或安全的配置管理工具

2. **传输安全**
   - 所有API接口使用HTTPS协议
   - 与微信支付平台通信时严格遵循其安全要求

3. **签名验证**
   - 实现微信支付的签名算法，验证所有支付通知的真实性
   - 为管理员API添加签名验证机制

### 访问控制

1. **用户认证**
   - 继续使用现有的JWT认证机制
   - 为不同权限级别设计不同的令牌

2. **管理员权限**
   - 实现基于角色的访问控制(RBAC)
   - 定义超级管理员、普通管理员和操作员角色
   - 敏感操作需要额外的权限验证

3. **API权限**
   - 为每个API端点定义所需的权限级别
   - 实现中间件进行权限检查

### 防护措施

1. **防止重放攻击**
   - 为关键请求添加nonce和时间戳
   - 实现请求幂等性处理

2. **限流措施**
   - 为API接口实现速率限制
   - 针对敏感操作设置更严格的限制

3. **异常监控**
   - 记录所有异常和安全相关事件
   - 实现自动告警机制

## 错误处理

### 错误分类

1. **业务错误**
   - 权益不足
   - 卡密无效
   - 订单已过期
   - 等等

2. **系统错误**
   - 数据库连接失败
   - 外部服务不可用
   - 内部处理异常
   - 等等

3. **安全错误**
   - 认证失败
   - 权限不足
   - 签名验证失败
   - 等等

### 错误响应格式

所有API错误响应将使用统一的格式：

```json
{
  "success": false,
  "error_code": "string",
  "message": "string",
  "details": "object|null",
  "request_id": "string"
}
```

### 错误日志

1. **日志级别**
   - DEBUG: 详细的开发信息
   - INFO: 正常操作信息
   - WARNING: 潜在问题警告
   - ERROR: 错误但不影响系统运行
   - CRITICAL: 严重错误需要立即处理

2. **日志内容**
   - 时间戳
   - 错误级别
   - 请求ID
   - 用户ID（如适用）
   - 错误代码
   - 错误消息
   - 堆栈跟踪（仅在开发环境）

## 测试策略

### 单元测试

1. **测试范围**
   - 所有服务类的公共方法
   - 数据访问层的关键方法
   - 工具函数和辅助类

2. **测试工具**
   - pytest作为测试框架
   - unittest.mock用于模拟依赖

### 集成测试

1. **测试范围**
   - API端点
   - 服务间协作
   - 数据库交互

2. **测试环境**
   - 独立的测试数据库
   - 微信支付沙箱环境

### 端到端测试

1. **测试范围**
   - 完整的支付流程
   - 卡密兑换流程
   - 权益消耗流程

2. **测试工具**
   - 自动化测试脚本
   - 模拟客户端请求

## 部署与配置

### 环境配置

1. **开发环境**
   - 使用全新的独立本地数据库，与现有的resume_service数据库完全隔离
   - 基于现有models结构创建新的数据库schema
   - 微信支付沙箱环境
   - 详细日志记录

2. **测试环境**
   - 独立的测试数据库，不使用现有的数据库
   - 确保测试过程不会影响现有系统的数据
   - 微信支付沙箱环境
   - 模拟生产配置

3. **生产环境**
   - 可以使用现有数据库但通过迁移脚本安全地添加新表和字段
   - 真实微信支付环境
   - 优化的日志级别

### 配置项

1. **数据库配置**
   ```
   DATABASE_URL=mysql+pymysql://user:password@host/dbname
   TEST_DATABASE_URL=mysql+pymysql://user:password@host/test_dbname
   ```

2. **微信支付配置**
   ```
   # 默认商户配置（向后兼容）
   WECHAT_APP_ID=your_app_id
   WECHAT_APP_SECRET=your_app_secret
   WECHAT_MCH_ID=your_mch_id
   WECHAT_MCH_KEY=your_mch_key
   WECHAT_NOTIFY_URL=https://your-domain.com/api/payment/notify
   
   # 多商户配置将存储在数据库中
   ```

3. **安全配置**
   ```
   JWT_SECRET_KEY=your_jwt_secret
   ADMIN_API_KEY=your_admin_api_key
   ENCRYPTION_KEY=your_encryption_key
   ```

4. **系统配置**
   ```
   LOG_LEVEL=INFO
   RATE_LIMIT_PER_MINUTE=60
   CLEANUP_INTERVAL_HOURS=24
   ```

### 数据库迁移与隔离

为确保开发和测试过程不影响现有系统，我们将采用以下策略：

1. **创建独立的测试数据库**
   ```bash
   # 创建新的数据库
   CREATE DATABASE wechat_payment_test;
   
   # 为新数据库创建用户并授权
   CREATE USER 'payment_user'@'localhost' IDENTIFIED BY 'SecurePassword123';
   GRANT ALL PRIVILEGES ON wechat_payment_test.* TO 'payment_user'@'localhost';
   FLUSH PRIVILEGES;
   ```

2. **配置数据库连接**
   ```python
   # 在配置文件中使用不同的数据库URL
   DATABASE_URL = "mysql+pymysql://resume_user:Resume123!@localhost/resume_service"  # 生产环境
   TEST_DATABASE_URL = "mysql+pymysql://payment_user:SecurePassword123@localhost/wechat_payment_test"  # 测试环境
   
   # 根据环境变量选择数据库
   if os.getenv("ENVIRONMENT") == "test":
       SQLALCHEMY_DATABASE_URL = TEST_DATABASE_URL
   else:
       SQLALCHEMY_DATABASE_URL = DATABASE_URL
   ```

3. **使用Alembic进行数据库迁移管理**
   ```bash
   # 创建迁移脚本
   alembic revision --autogenerate -m "Add payment tables"
   
   # 应用迁移到测试数据库
   ENVIRONMENT=test alembic upgrade head
   
   # 应用迁移到生产数据库（仅在充分测试后）
   alembic upgrade head
   
   # 回滚迁移（如需要）
   alembic downgrade -1
   ```

4. **数据库初始化脚本**
   
   创建一个专门的初始化脚本，用于设置测试环境：
   
   ```python
   # init_test_database.py
   import os
   from sqlalchemy import create_engine
   from app.database import Base
   from config.fastapi_config import settings
   
   # 设置环境变量
   os.environ["ENVIRONMENT"] = "test"
   
   # 创建引擎
   engine = create_engine(settings.TEST_DATABASE_URL)
   
   # 创建所有表
   Base.metadata.create_all(bind=engine)
   
   print("测试数据库初始化完成")
   ```

5. **数据隔离策略**
   
   - 所有与支付相关的新表将在独立的测试数据库中开发和测试
   - 对现有表的修改将先在测试环境验证，确保兼容性
   - 生产部署前进行完整的数据迁移测试
   - 保留回滚脚本以应对潜在问题

## 扩展性考虑

### 未来功能扩展

1. **支付方式扩展**
   - 设计支付接口时考虑未来添加其他支付方式（如支付宝）
   - 使用策略模式实现不同支付方式的处理

2. **会员等级扩展**
   - 数据模型设计支持多级会员体系
   - 权益系统支持不同等级的差异化权益

3. **多语言支持**
   - 错误消息和通知内容设计为可国际化
   - 使用翻译文件存储不同语言的文本

### 性能优化

1. **缓存策略**
   - 为频繁访问的数据实现缓存（如用户权益、商户配置）
   - 使用Redis作为缓存存储

2. **异步处理**
   - 使用任务队列处理耗时操作（如通知发送、统计生成）
   - 实现异步通知重试机制

3. **数据库优化**
   - 为查询频繁的字段创建索引
   - 实现数据分区策略处理大量历史数据

## 风险与缓解措施

### 已识别风险

1. **支付平台不可用**
   - **风险**: 微信支付平台暂时不可用导致支付失败
   - **缓解**: 实现重试机制和状态恢复，提供清晰的用户提示

2. **数据不一致**
   - **风险**: 支付成功但系统未正确更新订单状态
   - **缓解**: 实现定期对账和状态修复机制，记录详细的事务日志

3. **安全漏洞**
   - **风险**: 系统存在安全漏洞被攻击者利用
   - **缓解**: 定期安全审计，遵循安全最佳实践，及时更新依赖库

4. **性能瓶颈**
   - **风险**: 高并发场景下系统性能下降
   - **缓解**: 负载测试识别瓶颈，实现水平扩展，优化关键路径

### 应急预案

1. **支付异常处理**
   - 建立支付异常监控机制
   - 准备手动对账和状态修复工具
   - 制定用户沟通和补偿策略

2. **系统降级策略**
   - 定义关键功能和非关键功能
   - 实现功能降级开关
   - 准备静态页面用于系统维护期间

3. **数据恢复计划**
   - 实施定期数据备份策略
   - 测试数据恢复流程
   - 记录所有数据变更的审计日志