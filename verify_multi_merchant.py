#!/usr/bin/env python3
"""
多商户系统验证脚本
"""
import requests
import json
import sys

def test_api_endpoint(url, expected_status=200, description=""):
    """测试API端点"""
    try:
        response = requests.get(url, timeout=10)
        status = "✅" if response.status_code == expected_status else "❌"
        print(f"{status} {description}")
        print(f"   URL: {url}")
        print(f"   状态码: {response.status_code} (期望: {expected_status})")
        if response.status_code != expected_status:
            print(f"   响应: {response.text[:200]}")
        print()
        return response.status_code == expected_status
    except Exception as e:
        print(f"❌ {description}")
        print(f"   URL: {url}")
        print(f"   错误: {e}")
        print()
        return False

def main():
    """主函数"""
    print("🚀 多商户系统功能验证")
    print("=" * 50)
    
    base_url = "http://localhost:8000"
    
    # 测试用例
    tests = [
        # 豁免路径测试（不需要商户验证）
        (f"{base_url}/health/ping", 200, "健康检查（豁免路径）"),
        
        # 默认商户测试
        (f"{base_url}/auth/user", 403, "认证接口（默认商户，未登录）"),
        
        # 指定商户测试
        (f"{base_url}/auth/user?source=default", 403, "认证接口（指定默认商户，未登录）"),
        
        # 无效商户测试
        (f"{base_url}/auth/user?source=invalid_merchant", 400, "认证接口（无效商户）"),
        
        # 商户管理接口测试（需要管理员权限）
        (f"{base_url}/merchant/?source=default", 403, "商户管理接口（需要管理员权限）"),
    ]
    
    # 执行测试
    passed = 0
    total = len(tests)
    
    for url, expected_status, description in tests:
        if test_api_endpoint(url, expected_status, description):
            passed += 1
    
    # 输出结果
    print("=" * 50)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！多商户系统正常工作。")
        
        print("\n✨ 核心功能验证:")
        print("✅ 商户中间件正常工作")
        print("✅ 商户验证和路由正常")
        print("✅ 豁免路径正常工作")
        print("✅ 默认商户机制正常")
        print("✅ 无效商户检测正常")
        print("✅ 权限控制正常")
        
        print("\n📋 下一步建议:")
        print("1. 配置真实的微信小程序AppID和AppSecret")
        print("2. 配置微信支付证书文件")
        print("3. 创建新的商户配置")
        print("4. 在微信小程序中添加source参数")
        
        return 0
    else:
        print(f"❌ {total - passed} 个测试失败")
        return 1

if __name__ == "__main__":
    sys.exit(main())
