#!/usr/bin/env python3
"""
小程序来源映射表迁移脚本
创建miniprogram_sources表，用于映射小程序source标识到商户代码和app_id的关系
"""

import os
import sys
import logging
from sqlalchemy import create_engine, text, MetaData, inspect
from sqlalchemy.exc import SQLAlchemyError
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class MiniprogramSourceMigration:
    """小程序来源映射表迁移"""
    
    def __init__(self):
        # 从环境变量获取数据库配置
        self.database_url = os.getenv("DATABASE_URL")
        if not self.database_url:
            raise ValueError("DATABASE_URL 环境变量未设置")
        
        self.engine = create_engine(self.database_url)
        logger.info(f"连接数据库: {self.database_url}")
    
    def check_table_exists(self, table_name: str) -> bool:
        """检查表是否存在"""
        inspector = inspect(self.engine)
        return table_name in inspector.get_table_names()
    
    def create_miniprogram_sources_table(self):
        """创建小程序来源映射表"""
        logger.info("创建 miniprogram_sources 表...")
        
        create_table_sql = """
        CREATE TABLE miniprogram_sources (
            id INT AUTO_INCREMENT PRIMARY KEY,
            source_code VARCHAR(50) NOT NULL UNIQUE COMMENT '小程序来源标识',
            merchant_id INT NOT NULL COMMENT '商户ID',
            app_id VARCHAR(50) NOT NULL COMMENT '微信小程序AppID',
            source_name VARCHAR(100) COMMENT '来源名称',
            description TEXT COMMENT '描述',
            is_active BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否启用',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
            
            INDEX idx_source_code (source_code),
            INDEX idx_merchant_id (merchant_id),
            INDEX idx_app_id (app_id),
            INDEX idx_is_active (is_active),
            
            FOREIGN KEY (merchant_id) REFERENCES merchants(id) ON DELETE CASCADE
        ) COMMENT='小程序来源映射表，用于映射source参数到商户和AppID'
        """
        
        with self.engine.connect() as conn:
            conn.execute(text(create_table_sql))
            conn.commit()
        
        logger.info("✅ miniprogram_sources 表创建成功")
    
    def insert_default_mappings(self):
        """插入默认的映射关系"""
        logger.info("插入默认映射关系...")
        
        # 查询现有商户
        with self.engine.connect() as conn:
            # 获取默认商户
            result = conn.execute(text("""
                SELECT id, merchant_code FROM merchants 
                WHERE merchant_code = 'default' AND status = 'active'
                LIMIT 1
            """))
            default_merchant = result.fetchone()
            
            if not default_merchant:
                logger.warning("未找到默认商户，跳过默认映射创建")
                return
            
            merchant_id = default_merchant[0]
            
            # 获取该商户的微信配置
            result = conn.execute(text("""
                SELECT app_id FROM merchant_wechat_configs 
                WHERE merchant_id = :merchant_id AND is_active = TRUE
                LIMIT 1
            """), {"merchant_id": merchant_id})
            wechat_config = result.fetchone()
            
            if not wechat_config:
                logger.warning("默认商户没有微信配置，跳过默认映射创建")
                return
            
            app_id = wechat_config[0]
            
            # 插入默认映射
            default_mappings = [
                {
                    "source_code": "default",
                    "merchant_id": merchant_id,
                    "app_id": app_id,
                    "source_name": "默认小程序",
                    "description": "默认的小程序来源映射"
                },
                {
                    "source_code": "huahuaban",
                    "merchant_id": merchant_id,
                    "app_id": app_id,
                    "source_name": "花花板小程序",
                    "description": "花花板小程序来源映射"
                }
            ]
            
            for mapping in default_mappings:
                # 检查是否已存在
                result = conn.execute(text("""
                    SELECT id FROM miniprogram_sources 
                    WHERE source_code = :source_code
                """), {"source_code": mapping["source_code"]})
                
                if result.fetchone():
                    logger.info(f"映射 {mapping['source_code']} 已存在，跳过")
                    continue
                
                # 插入新映射
                conn.execute(text("""
                    INSERT INTO miniprogram_sources 
                    (source_code, merchant_id, app_id, source_name, description)
                    VALUES (:source_code, :merchant_id, :app_id, :source_name, :description)
                """), mapping)
                
                logger.info(f"✅ 创建映射: {mapping['source_code']} -> 商户ID:{mapping['merchant_id']}, AppID:{mapping['app_id']}")
            
            conn.commit()
    
    def run_migration(self):
        """执行迁移"""
        try:
            logger.info("开始小程序来源映射表迁移...")
            
            # 检查merchants表是否存在
            if not self.check_table_exists("merchants"):
                raise Exception("merchants表不存在，请先运行商户表迁移")
            
            # 检查merchant_wechat_configs表是否存在
            if not self.check_table_exists("merchant_wechat_configs"):
                raise Exception("merchant_wechat_configs表不存在，请先运行商户配置表迁移")
            
            # 创建miniprogram_sources表
            if self.check_table_exists("miniprogram_sources"):
                logger.info("miniprogram_sources表已存在，跳过创建")
            else:
                self.create_miniprogram_sources_table()
            
            # 插入默认映射
            self.insert_default_mappings()
            
            logger.info("✅ 小程序来源映射表迁移完成")
            
        except Exception as e:
            logger.error(f"❌ 迁移失败: {str(e)}")
            raise

def main():
    """主函数"""
    try:
        migration = MiniprogramSourceMigration()
        migration.run_migration()
        print("\n🎉 小程序来源映射表迁移成功完成！")
        
    except Exception as e:
        print(f"\n❌ 迁移失败: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
