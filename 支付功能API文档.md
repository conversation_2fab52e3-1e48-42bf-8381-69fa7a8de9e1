
## 7. 支付模块 (/payment)

### 7.1 获取会员套餐列表
**接口**: `GET /payment/plans`
**描述**: 获取可用的会员套餐列表
**认证**: 不需要

**请求参数**:
- `active_only` (查询参数, 可选): 是否只获取启用的套餐，默认为true

**响应示例**:
```json
{
  "plans": [
    {
      "id": 1,
      "name": "月度会员",
      "description": "享受一个月的会员特权",
      "price": 1980,
      "original_price": 2980,
      "duration_days": 30,
      "features": ["无限制简历导出", "高级模板使用", "证件照生成", "优先客服支持"],
      "is_active": true,
      "is_recommended": false,
      "sort_order": 1,
      "discount_rate": 33.6,
      "price_yuan": 19.8,
      "original_price_yuan": 29.8,
      "created_at": "2024-01-01T00:00:00",
      "updated_at": "2024-01-01T00:00:00"
    }
  ],
  "total": 3
}
```

### 7.2 创建订单
**接口**: `POST /payment/create-order`
**描述**: 创建会员购买订单
**认证**: 需要

**请求参数**:
```json
{
  "plan_id": 1,
  "client_ip": "127.0.0.1",
  "user_agent": "Mozilla/5.0..."
}
```

**参数说明**:
- `plan_id` (必填): 套餐ID
- `client_ip` (可选): 客户端IP
- `user_agent` (可选): 用户代理

**响应示例**:
```json
{
  "id": "RS20241201123456ABCD1234",
  "user_id": 1,
  "plan_id": 1,
  "amount": 1980,
  "original_amount": 2980,
  "discount_amount": 1000,
  "status": "pending",
  "expired_at": "2024-12-01T13:04:56",
  "plan_name": "月度会员",
  "amount_yuan": 19.8,
  "created_at": "2024-12-01T12:34:56",
  "updated_at": "2024-12-01T12:34:56"
}
```

### 7.3 获取用户订单列表
**接口**: `GET /payment/orders`
**描述**: 获取当前用户的订单列表
**认证**: 需要

**请求参数**:
- `status_filter` (查询参数, 可选): 订单状态过滤
- `limit` (查询参数, 可选): 每页数量，默认20，最大100
- `offset` (查询参数, 可选): 偏移量，默认0

**响应示例**:
```json
{
  "orders": [
    {
      "id": "RS20241201123456ABCD1234",
      "user_id": 1,
      "plan_id": 1,
      "amount": 1980,
      "status": "pending",
      "plan_name": "月度会员",
      "amount_yuan": 19.8,
      "created_at": "2024-12-01T12:34:56"
    }
  ],
  "total": 1,
  "has_more": false
}
```

### 7.4 获取订单详情
**接口**: `GET /payment/order/{order_id}`
**描述**: 获取指定订单的详细信息
**认证**: 需要

**路径参数**:
- `order_id` (必填): 订单ID

**响应示例**:
```json
{
  "id": "RS20241201123456ABCD1234",
  "user_id": 1,
  "plan_id": 1,
  "amount": 1980,
  "original_amount": 2980,
  "discount_amount": 1000,
  "status": "pending",
  "wx_prepay_id": "wx123456789",
  "expired_at": "2024-12-01T13:04:56",
  "plan_name": "月度会员",
  "amount_yuan": 19.8,
  "created_at": "2024-12-01T12:34:56",
  "updated_at": "2024-12-01T12:34:56"
}
```

### 7.5 创建微信支付
**接口**: `POST /payment/wechat-pay`
**描述**: 为指定订单创建微信支付参数
**认证**: 需要

**请求参数**:
```json
{
  "order_id": "RS20241201123456ABCD1234"
}
```

**参数说明**:
- `order_id` (必填): 订单ID

**响应示例**:
```json
{
  "appId": "wx1234567890abcdef",
  "timeStamp": "1701234567",
  "nonceStr": "abcd1234efgh5678",
  "package": "prepay_id=wx123456789",
  "signType": "RSA",
  "paySign": "signature_string",
  "order_id": "RS20241201123456ABCD1234"
}
```

### 7.6 取消订单
**接口**: `POST /payment/cancel-order/{order_id}`
**描述**: 取消指定的未支付订单
**认证**: 需要

**路径参数**:
- `order_id` (必填): 订单ID

**请求参数**:
```json
{
  "cancel_reason": "用户主动取消"
}
```

**参数说明**:
- `cancel_reason` (可选): 取消原因

**响应示例**:
```json
{
  "success": true,
  "message": "订单取消成功"
}
```

### 7.7 查询微信订单状态
**接口**: `GET /payment/query-order/{order_id}`
**描述**: 查询微信支付平台上的订单状态
**认证**: 需要

**路径参数**:
- `order_id` (必填): 订单ID

**响应示例**:
```json
{
  "success": true,
  "message": "查询成功",
  "data": {
    "out_trade_no": "RS20241201123456ABCD1234",
    "transaction_id": "4200001234567890",
    "trade_state": "SUCCESS",
    "trade_state_desc": "支付成功"
  }
}
```

### 7.8 微信支付回调
**接口**: `POST /payment/callback`
**描述**: 微信支付平台回调接口，用于接收支付结果通知
**认证**: 不需要（由微信支付平台调用）

**响应示例**:
```json
{
  "code": "SUCCESS",
  "message": "成功"
}
```

## 8. 会员管理模块 (/membership)

### 8.1 获取会员状态
**接口**: `GET /membership/status`
**描述**: 获取当前用户的会员状态和信息
**认证**: 需要

**响应示例**:
```json
{
  "is_member": true,
  "current_membership": {
    "id": 1,
    "user_id": 1,
    "plan_id": 1,
    "plan_name": "月度会员",
    "start_date": "2024-12-01T12:34:56",
    "end_date": "2024-12-31T12:34:56",
    "is_active": true,
    "remaining_days": 30
  },
  "membership_history": []
}
```

### 8.2 获取会员权益
**接口**: `GET /membership/benefits`
**描述**: 获取当前用户的会员权益信息
**认证**: 需要

**响应示例**:
```json
{
  "is_member": true,
  "plan_name": "月度会员",
  "end_date": "2024-12-31T12:34:56",
  "remaining_days": 30,
  "benefits": ["无限制简历导出", "高级模板使用", "证件照生成", "优先客服支持"],
  "message": "您是月度会员用户，还有30天到期"
}
```

### 8.3 检查功能权限
**接口**: `GET /membership/check-permission/{feature}`
**描述**: 检查当前用户是否有权限使用指定功能
**认证**: 需要

**路径参数**:
- `feature` (必填): 功能名称，如resume_export, idphoto_generate, premium_templates

**响应示例**:
```json
{
  "has_permission": true,
  "is_member": true,
  "plan_name": "月度会员",
  "message": "权限验证成功",
  "upgrade_required": false
}
```

### 8.4 同步会员状态
**接口**: `POST /membership/sync-status`
**描述**: 同步用户会员状态（检查过期等）
**认证**: 需要

**响应示例**:
```json
{
  "success": true,
  "is_member": true,
  "message": "会员状态同步成功",
  "expired_count": 0
}
```

### 8.5 获取使用统计
**接口**: `GET /membership/usage-stats`
**描述**: 获取当前用户的功能使用统计
**认证**: 需要

**响应示例**:
```json
{
  "is_member": true,
  "stats_period": "最近30天",
  "usage_stats": {
    "resume_exports": 15,
    "idphoto_generates": 8,
    "template_uses": 5,
    "total_actions": 28
  },
  "membership_info": {
    "plan_name": "月度会员",
    "end_date": "2024-12-31T12:34:56",
    "remaining_days": 30
  }
}
```

## 9. 支付安全模块 (/payment/security)

### 9.1 检查订单创建权限
**接口**: `GET /payment/security/check-order-permission`
**描述**: 检查当前用户是否有权限创建新订单
**认证**: 需要

**响应示例**:
```json
{
  "can_create_order": true,
  "message": "验证通过",
  "user_id": 1,
  "client_ip": "127.0.0.1"
}
```

### 9.2 检查支付权限
**接口**: `GET /payment/security/check-payment-permission/{order_id}`
**描述**: 检查当前用户是否有权限支付指定订单
**认证**: 需要

**路径参数**:
- `order_id` (必填): 订单ID

**响应示例**:
```json
{
  "can_pay": true,
  "message": "验证通过",
  "order_id": "RS20241201123456ABCD1234",
  "user_id": 1
}
```

### 9.3 获取安全提示
**接口**: `GET /payment/security/security-tips`
**描述**: 获取支付安全提示信息
**认证**: 不需要

**响应示例**:
```json
{
  "security_tips": [
    {
      "title": "订单安全",
      "tips": [
        "不要频繁创建订单",
        "及时完成或取消未支付订单",
        "注意订单过期时间"
      ]
    },
    {
      "title": "支付安全",
      "tips": [
        "确认订单信息后再支付",
        "不要重复点击支付按钮",
        "支付完成后及时查看结果"
      ]
    }
  ],
  "emergency_contact": {
    "customer_service": "在线客服",
    "report_issue": "问题反馈功能"
  }
}
```
