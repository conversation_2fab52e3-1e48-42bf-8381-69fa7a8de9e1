#!/usr/bin/env python3
"""
卡密管理命令行工具
"""
import sys
import argparse
import mysql.connector
from mysql.connector import Error
import logging
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
import json
import csv

# 尝试导入tabulate，如果没有则使用简单的表格显示
try:
    from tabulate import tabulate
    HAS_TABULATE = True
except ImportError:
    HAS_TABULATE = False

    def tabulate(data, headers=None, tablefmt='grid'):
        """简单的表格显示函数"""
        if not data:
            return ""

        # 计算每列的最大宽度
        if headers:
            all_data = [headers] + data
        else:
            all_data = data

        col_widths = []
        if all_data:
            for i in range(len(all_data[0])):
                max_width = max(len(str(row[i])) for row in all_data)
                col_widths.append(max_width)

        # 生成表格
        lines = []
        if headers:
            # 标题行
            header_line = " | ".join(str(headers[i]).ljust(col_widths[i]) for i in range(len(headers)))
            lines.append(header_line)
            lines.append("-" * len(header_line))

        # 数据行
        for row in data:
            data_line = " | ".join(str(row[i]).ljust(col_widths[i]) for i in range(len(row)))
            lines.append(data_line)

        return "\n".join(lines)

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 数据库连接配置
DB_CONFIG = {
    'host': 'localhost',
    'database': 'resume_service',
    'user': 'resume_user',
    'password': 'Resume123!'
}


class CardManager:
    """卡密管理器"""
    
    def __init__(self):
        self.connection = None
        self.cursor = None
    
    def connect(self):
        """连接数据库"""
        try:
            self.connection = mysql.connector.connect(**DB_CONFIG)
            self.cursor = self.connection.cursor(dictionary=True)
            logger.info("数据库连接成功")
            return True
        except Error as e:
            logger.error(f"数据库连接失败: {e}")
            return False
    
    def disconnect(self):
        """断开数据库连接"""
        if self.cursor:
            self.cursor.close()
        if self.connection:
            self.connection.close()
        logger.info("数据库连接已关闭")
    
    def create_batch(self, batch_name: str, benefit_type: str, benefit_value: int, 
                    benefit_unit: str, card_count: int, expires_days: Optional[int] = None,
                    description: Optional[str] = None, notes: Optional[str] = None) -> bool:
        """创建卡密批次"""
        try:
            # 生成批次编码
            timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
            batch_code = f"BATCH_{timestamp}"
            
            # 计算过期时间
            expires_at = None
            if expires_days:
                expires_at = datetime.now() + timedelta(days=expires_days)
            
            # 插入批次记录
            batch_sql = """
            INSERT INTO card_batches (
                batch_name, batch_code, benefit_type, benefit_value, benefit_unit,
                benefit_description, total_cards, expires_at, notes
            ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
            """
            
            batch_values = (
                batch_name, batch_code, benefit_type, benefit_value, benefit_unit,
                description, card_count, expires_at, notes
            )
            
            self.cursor.execute(batch_sql, batch_values)
            batch_id = self.cursor.lastrowid
            
            # 生成卡密
            import secrets
            import string
            
            chars = string.ascii_uppercase + string.digits
            chars = chars.replace('0', '').replace('O', '').replace('1', '').replace('I', '')
            
            cards_data = []
            generated_codes = []
            
            for _ in range(card_count):
                # 生成唯一卡密码
                while True:
                    card_code = ''.join(secrets.choice(chars) for _ in range(16))
                    # 检查是否已存在
                    check_sql = "SELECT COUNT(*) as count FROM cards WHERE card_code = %s"
                    self.cursor.execute(check_sql, (card_code,))
                    if self.cursor.fetchone()['count'] == 0:
                        break
                
                cards_data.append((batch_id, card_code, expires_at))
                generated_codes.append(card_code)
            
            # 批量插入卡密
            card_sql = """
            INSERT INTO cards (batch_id, card_code, expires_at)
            VALUES (%s, %s, %s)
            """
            
            self.cursor.executemany(card_sql, cards_data)
            self.connection.commit()
            
            print(f"✅ 成功创建批次: {batch_name}")
            print(f"   批次编码: {batch_code}")
            print(f"   权益类型: {benefit_type}")
            print(f"   权益数值: {benefit_value} {benefit_unit}")
            print(f"   生成卡密: {card_count} 张")
            
            # 询问是否导出卡密
            export = input("\n是否导出卡密到文件? (y/N): ").lower().strip()
            if export == 'y':
                self._export_cards(batch_code, generated_codes, batch_name, benefit_type, benefit_value, benefit_unit)
            
            return True
            
        except Error as e:
            logger.error(f"创建批次失败: {e}")
            if self.connection:
                self.connection.rollback()
            return False
    
    def _export_cards(self, batch_code: str, card_codes: List[str], batch_name: str,
                     benefit_type: str, benefit_value: int, benefit_unit: str):
        """导出卡密到文件"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            
            # 导出为CSV
            csv_filename = f"cards_{batch_code}_{timestamp}.csv"
            with open(csv_filename, 'w', newline='', encoding='utf-8') as csvfile:
                writer = csv.writer(csvfile)
                writer.writerow(['卡密码', '批次名称', '权益类型', '权益数值', '权益单位', '生成时间'])
                
                for card_code in card_codes:
                    writer.writerow([
                        card_code, batch_name, benefit_type, benefit_value, benefit_unit,
                        datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                    ])
            
            # 导出为JSON
            json_filename = f"cards_{batch_code}_{timestamp}.json"
            export_data = {
                "batch_info": {
                    "batch_code": batch_code,
                    "batch_name": batch_name,
                    "benefit_type": benefit_type,
                    "benefit_value": benefit_value,
                    "benefit_unit": benefit_unit,
                    "total_cards": len(card_codes),
                    "generated_at": datetime.now().isoformat()
                },
                "cards": card_codes
            }
            
            with open(json_filename, 'w', encoding='utf-8') as jsonfile:
                json.dump(export_data, jsonfile, ensure_ascii=False, indent=2)
            
            print(f"✅ 卡密已导出:")
            print(f"   CSV文件: {csv_filename}")
            print(f"   JSON文件: {json_filename}")
            
        except Exception as e:
            logger.error(f"导出卡密失败: {e}")
    
    def list_batches(self, status: Optional[str] = None, limit: int = 20):
        """列出卡密批次"""
        try:
            sql = """
            SELECT id, batch_name, batch_code, benefit_type, benefit_value, benefit_unit,
                   total_cards, used_cards, status, created_at, expires_at
            FROM card_batches
            """
            
            params = []
            if status:
                sql += " WHERE status = %s"
                params.append(status)
            
            sql += " ORDER BY created_at DESC LIMIT %s"
            params.append(limit)
            
            self.cursor.execute(sql, params)
            batches = self.cursor.fetchall()
            
            if not batches:
                print("📭 没有找到卡密批次")
                return
            
            # 格式化数据
            table_data = []
            for batch in batches:
                unused_cards = batch['total_cards'] - batch['used_cards']
                usage_rate = (batch['used_cards'] / batch['total_cards'] * 100) if batch['total_cards'] > 0 else 0
                
                expires_str = batch['expires_at'].strftime("%Y-%m-%d") if batch['expires_at'] else "永不过期"
                
                table_data.append([
                    batch['id'],
                    batch['batch_name'][:20] + "..." if len(batch['batch_name']) > 20 else batch['batch_name'],
                    batch['batch_code'],
                    f"{batch['benefit_value']} {batch['benefit_unit']}",
                    batch['total_cards'],
                    batch['used_cards'],
                    unused_cards,
                    f"{usage_rate:.1f}%",
                    batch['status'],
                    expires_str
                ])
            
            headers = ['ID', '批次名称', '批次编码', '权益', '总数', '已用', '剩余', '使用率', '状态', '过期时间']
            print("\n📊 卡密批次列表:")
            print(tabulate(table_data, headers=headers, tablefmt='grid'))
            
        except Error as e:
            logger.error(f"查询批次列表失败: {e}")
    
    def get_statistics(self):
        """获取统计信息"""
        try:
            # 批次统计
            batch_sql = """
            SELECT 
                COUNT(*) as total_batches,
                SUM(CASE WHEN status = 'active' THEN 1 ELSE 0 END) as active_batches,
                SUM(CASE WHEN status = 'disabled' THEN 1 ELSE 0 END) as disabled_batches
            FROM card_batches
            """
            
            self.cursor.execute(batch_sql)
            batch_stats = self.cursor.fetchone()
            
            # 卡密统计
            card_sql = """
            SELECT 
                COUNT(*) as total_cards,
                SUM(CASE WHEN status = 'unused' THEN 1 ELSE 0 END) as unused_cards,
                SUM(CASE WHEN status = 'used' THEN 1 ELSE 0 END) as used_cards,
                SUM(CASE WHEN status = 'expired' THEN 1 ELSE 0 END) as expired_cards,
                SUM(CASE WHEN status = 'disabled' THEN 1 ELSE 0 END) as disabled_cards
            FROM cards
            """
            
            self.cursor.execute(card_sql)
            card_stats = self.cursor.fetchone()
            
            # 权益类型统计
            benefit_sql = """
            SELECT benefit_type, COUNT(*) as count, SUM(total_cards) as total_cards
            FROM card_batches
            GROUP BY benefit_type
            """
            
            self.cursor.execute(benefit_sql)
            benefit_stats = self.cursor.fetchall()
            
            # 最近使用统计
            recent_sql = """
            SELECT DATE(used_at) as use_date, COUNT(*) as count
            FROM cards
            WHERE used_at IS NOT NULL AND used_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
            GROUP BY DATE(used_at)
            ORDER BY use_date DESC
            """
            
            self.cursor.execute(recent_sql)
            recent_usage = self.cursor.fetchall()
            
            # 显示统计信息
            print("\n📈 卡密系统统计信息")
            print("=" * 50)
            
            print(f"\n📦 批次统计:")
            print(f"   总批次数: {batch_stats['total_batches']}")
            print(f"   活跃批次: {batch_stats['active_batches']}")
            print(f"   禁用批次: {batch_stats['disabled_batches']}")
            
            print(f"\n🎫 卡密统计:")
            print(f"   总卡密数: {card_stats['total_cards']}")
            print(f"   未使用: {card_stats['unused_cards']}")
            print(f"   已使用: {card_stats['used_cards']}")
            print(f"   已过期: {card_stats['expired_cards']}")
            print(f"   已禁用: {card_stats['disabled_cards']}")
            
            if card_stats['total_cards'] > 0:
                usage_rate = (card_stats['used_cards'] / card_stats['total_cards'] * 100)
                print(f"   使用率: {usage_rate:.2f}%")
            
            print(f"\n🎁 权益类型统计:")
            for benefit in benefit_stats:
                print(f"   {benefit['benefit_type']}: {benefit['count']} 批次, {benefit['total_cards']} 张卡密")
            
            if recent_usage:
                print(f"\n📅 最近7天使用情况:")
                for usage in recent_usage:
                    print(f"   {usage['use_date']}: {usage['count']} 张")
            
        except Error as e:
            logger.error(f"获取统计信息失败: {e}")
    
    def disable_batch(self, batch_id: int):
        """禁用批次"""
        try:
            # 检查批次是否存在
            check_sql = "SELECT batch_name, status FROM card_batches WHERE id = %s"
            self.cursor.execute(check_sql, (batch_id,))
            batch = self.cursor.fetchone()
            
            if not batch:
                print(f"❌ 批次 {batch_id} 不存在")
                return False
            
            if batch['status'] == 'disabled':
                print(f"⚠️  批次 '{batch['batch_name']}' 已经是禁用状态")
                return True
            
            # 禁用批次
            update_batch_sql = "UPDATE card_batches SET status = 'disabled' WHERE id = %s"
            self.cursor.execute(update_batch_sql, (batch_id,))
            
            # 禁用该批次下所有未使用的卡密
            update_cards_sql = """
            UPDATE cards SET status = 'disabled' 
            WHERE batch_id = %s AND status = 'unused'
            """
            self.cursor.execute(update_cards_sql, (batch_id,))
            
            disabled_cards = self.cursor.rowcount
            self.connection.commit()
            
            print(f"✅ 成功禁用批次 '{batch['batch_name']}'")
            print(f"   同时禁用了 {disabled_cards} 张未使用的卡密")
            
            return True
            
        except Error as e:
            logger.error(f"禁用批次失败: {e}")
            if self.connection:
                self.connection.rollback()
            return False
    
    def search_cards(self, keyword: str, limit: int = 20):
        """搜索卡密"""
        try:
            sql = """
            SELECT c.id, c.card_code, c.status, c.used_at, c.used_by,
                   b.batch_name, b.benefit_type, b.benefit_value, b.benefit_unit
            FROM cards c
            JOIN card_batches b ON c.batch_id = b.id
            WHERE c.card_code LIKE %s OR b.batch_name LIKE %s
            ORDER BY c.created_at DESC
            LIMIT %s
            """
            
            search_pattern = f"%{keyword}%"
            self.cursor.execute(sql, (search_pattern, search_pattern, limit))
            cards = self.cursor.fetchall()
            
            if not cards:
                print(f"📭 没有找到包含 '{keyword}' 的卡密")
                return
            
            # 格式化数据
            table_data = []
            for card in cards:
                used_info = "未使用"
                if card['used_at']:
                    used_info = f"已使用 ({card['used_at'].strftime('%Y-%m-%d')})"
                
                table_data.append([
                    card['id'],
                    card['card_code'],
                    card['batch_name'][:15] + "..." if len(card['batch_name']) > 15 else card['batch_name'],
                    f"{card['benefit_value']} {card['benefit_unit']}",
                    card['status'],
                    used_info
                ])
            
            headers = ['ID', '卡密码', '批次名称', '权益', '状态', '使用情况']
            print(f"\n🔍 搜索结果 (关键词: {keyword}):")
            print(tabulate(table_data, headers=headers, tablefmt='grid'))
            
        except Error as e:
            logger.error(f"搜索卡密失败: {e}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='卡密管理命令行工具')
    subparsers = parser.add_subparsers(dest='command', help='可用命令')

    # 创建批次命令
    create_parser = subparsers.add_parser('create', help='创建卡密批次')
    create_parser.add_argument('--name', required=True, help='批次名称')
    create_parser.add_argument('--type', choices=['quota', 'membership'], required=True, help='权益类型')
    create_parser.add_argument('--value', type=int, required=True, help='权益数值')
    create_parser.add_argument('--unit', choices=['times', 'days', 'months'], required=True, help='权益单位')
    create_parser.add_argument('--count', type=int, required=True, help='生成卡密数量')
    create_parser.add_argument('--expires', type=int, help='过期天数（可选）')
    create_parser.add_argument('--description', help='权益描述（可选）')
    create_parser.add_argument('--notes', help='备注信息（可选）')

    # 列出批次命令
    list_parser = subparsers.add_parser('list', help='列出卡密批次')
    list_parser.add_argument('--status', choices=['active', 'disabled'], help='按状态筛选')
    list_parser.add_argument('--limit', type=int, default=20, help='显示数量限制')

    # 统计信息命令
    subparsers.add_parser('stats', help='显示统计信息')

    # 禁用批次命令
    disable_parser = subparsers.add_parser('disable', help='禁用卡密批次')
    disable_parser.add_argument('batch_id', type=int, help='批次ID')

    # 搜索卡密命令
    search_parser = subparsers.add_parser('search', help='搜索卡密')
    search_parser.add_argument('keyword', help='搜索关键词')
    search_parser.add_argument('--limit', type=int, default=20, help='显示数量限制')

    # 导出批次命令
    export_parser = subparsers.add_parser('export', help='导出批次卡密')
    export_parser.add_argument('batch_id', type=int, help='批次ID')
    export_parser.add_argument('--format', choices=['csv', 'json', 'both'], default='both', help='导出格式')

    # 验证卡密命令
    verify_parser = subparsers.add_parser('verify', help='验证卡密')
    verify_parser.add_argument('card_code', help='卡密码')

    args = parser.parse_args()

    if not args.command:
        parser.print_help()
        return

    # 创建管理器并连接数据库
    manager = CardManager()
    if not manager.connect():
        print("❌ 数据库连接失败")
        return

    try:
        if args.command == 'create':
            manager.create_batch(
                batch_name=args.name,
                benefit_type=args.type,
                benefit_value=args.value,
                benefit_unit=args.unit,
                card_count=args.count,
                expires_days=args.expires,
                description=args.description,
                notes=args.notes
            )

        elif args.command == 'list':
            manager.list_batches(status=args.status, limit=args.limit)

        elif args.command == 'stats':
            manager.get_statistics()

        elif args.command == 'disable':
            manager.disable_batch(args.batch_id)

        elif args.command == 'search':
            manager.search_cards(args.keyword, limit=args.limit)

        elif args.command == 'export':
            export_batch_cards(manager, args.batch_id, args.format)

        elif args.command == 'verify':
            verify_card(manager, args.card_code)

    finally:
        manager.disconnect()


def export_batch_cards(manager: CardManager, batch_id: int, format_type: str):
    """导出批次卡密"""
    try:
        # 获取批次信息
        batch_sql = """
        SELECT batch_name, batch_code, benefit_type, benefit_value, benefit_unit, total_cards
        FROM card_batches WHERE id = %s
        """
        manager.cursor.execute(batch_sql, (batch_id,))
        batch = manager.cursor.fetchone()

        if not batch:
            print(f"❌ 批次 {batch_id} 不存在")
            return

        # 获取卡密列表
        cards_sql = """
        SELECT card_code, status, created_at, used_at, used_by
        FROM cards WHERE batch_id = %s
        ORDER BY created_at
        """
        manager.cursor.execute(cards_sql, (batch_id,))
        cards = manager.cursor.fetchall()

        if not cards:
            print(f"❌ 批次 {batch_id} 没有卡密")
            return

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        if format_type in ['csv', 'both']:
            # 导出CSV
            csv_filename = f"batch_{batch['batch_code']}_{timestamp}.csv"
            with open(csv_filename, 'w', newline='', encoding='utf-8') as csvfile:
                writer = csv.writer(csvfile)
                writer.writerow(['卡密码', '状态', '创建时间', '使用时间', '使用者ID'])

                for card in cards:
                    writer.writerow([
                        card['card_code'],
                        card['status'],
                        card['created_at'].strftime("%Y-%m-%d %H:%M:%S"),
                        card['used_at'].strftime("%Y-%m-%d %H:%M:%S") if card['used_at'] else '',
                        card['used_by'] or ''
                    ])

            print(f"✅ CSV文件已导出: {csv_filename}")

        if format_type in ['json', 'both']:
            # 导出JSON
            json_filename = f"batch_{batch['batch_code']}_{timestamp}.json"
            export_data = {
                "batch_info": {
                    "id": batch_id,
                    "batch_name": batch['batch_name'],
                    "batch_code": batch['batch_code'],
                    "benefit_type": batch['benefit_type'],
                    "benefit_value": batch['benefit_value'],
                    "benefit_unit": batch['benefit_unit'],
                    "total_cards": batch['total_cards'],
                    "exported_at": datetime.now().isoformat()
                },
                "cards": [
                    {
                        "card_code": card['card_code'],
                        "status": card['status'],
                        "created_at": card['created_at'].isoformat(),
                        "used_at": card['used_at'].isoformat() if card['used_at'] else None,
                        "used_by": card['used_by']
                    }
                    for card in cards
                ]
            }

            with open(json_filename, 'w', encoding='utf-8') as jsonfile:
                json.dump(export_data, jsonfile, ensure_ascii=False, indent=2)

            print(f"✅ JSON文件已导出: {json_filename}")

    except Error as e:
        logger.error(f"导出批次卡密失败: {e}")


def verify_card(manager: CardManager, card_code: str):
    """验证卡密"""
    try:
        sql = """
        SELECT c.*, b.batch_name, b.benefit_type, b.benefit_value, b.benefit_unit, b.status as batch_status
        FROM cards c
        JOIN card_batches b ON c.batch_id = b.id
        WHERE c.card_code = %s
        """

        manager.cursor.execute(sql, (card_code.upper(),))
        card = manager.cursor.fetchone()

        if not card:
            print(f"❌ 卡密 {card_code} 不存在")
            return

        print(f"\n🎫 卡密信息: {card_code}")
        print("=" * 50)
        print(f"批次名称: {card['batch_name']}")
        print(f"权益类型: {card['benefit_type']}")
        print(f"权益数值: {card['benefit_value']} {card['benefit_unit']}")
        print(f"卡密状态: {card['status']}")
        print(f"批次状态: {card['batch_status']}")
        print(f"创建时间: {card['created_at']}")

        if card['expires_at']:
            print(f"过期时间: {card['expires_at']}")
            if card['expires_at'] < datetime.now():
                print("⚠️  卡密已过期")
        else:
            print("过期时间: 永不过期")

        if card['used_at']:
            print(f"使用时间: {card['used_at']}")
            print(f"使用者ID: {card['used_by']}")

        # 验证有效性
        is_valid = True
        reasons = []

        if card['status'] != 'unused':
            is_valid = False
            reasons.append(f"卡密状态为 {card['status']}")

        if card['batch_status'] != 'active':
            is_valid = False
            reasons.append(f"批次状态为 {card['batch_status']}")

        if card['expires_at'] and card['expires_at'] < datetime.now():
            is_valid = False
            reasons.append("卡密已过期")

        print(f"\n验证结果: {'✅ 有效' if is_valid else '❌ 无效'}")
        if not is_valid:
            print("无效原因:")
            for reason in reasons:
                print(f"  - {reason}")

    except Error as e:
        logger.error(f"验证卡密失败: {e}")


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 操作已取消")
    except Exception as e:
        logger.error(f"程序执行异常: {e}")
        sys.exit(1)
