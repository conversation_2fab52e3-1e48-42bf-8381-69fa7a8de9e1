"""
FastAPI服务配置文件
统一管理FastAPI相关的所有配置项
"""
import os
from typing import List
from dotenv import load_dotenv

# 加载环境变量
load_dotenv(dotenv_path=os.path.join(os.path.dirname(__file__), '../', '.env'))

class FastAPISettings:
    """FastAPI服务设置"""

    # ===========================================
    # 应用基本配置
    # ===========================================
    APP_NAME: str = os.getenv("APP_NAME", "Resume Service API")
    VERSION: str = os.getenv("VERSION", "1.0.0")
    DEBUG: bool = os.getenv("DEBUG", "false").lower() == "true"

    # ===========================================
    # 服务器配置
    # ===========================================
    HOST: str = os.getenv("FASTAPI_HOST", "0.0.0.0")
    PORT: int = int(os.getenv("PORT", "18080"))
    FASTAPI_PORT: int = int(os.getenv("FASTAPI_PORT", "18080"))
    WORKERS: int = int(os.getenv("WORKERS", "2"))

    # ===========================================
    # 数据库配置
    # ===========================================
    DATABASE_URL: str = os.getenv(
        "DATABASE_URL",
        "mysql+pymysql://resume_user:Resume123!@localhost/resume_service"
    )
    
    # ===========================================
    # JWT配置
    # ===========================================
    SECRET_KEY: str = os.getenv("SECRET_KEY", "your-secret-key-change-in-production")
    ALGORITHM: str = os.getenv("ALGORITHM", "HS256")
    ACCESS_TOKEN_EXPIRE_MINUTES: int = int(os.getenv("ACCESS_TOKEN_EXPIRE_MINUTES", "30"))
    
    # ===========================================
    # 微信小程序配置
    # ===========================================
    WECHAT_APP_ID: str = os.getenv("WECHAT_APP_ID")
    WECHAT_APP_SECRET: str = os.getenv("WECHAT_APP_SECRET")
    WECHAT_API_URL: str = os.getenv("WECHAT_API_URL", "https://api.weixin.qq.com/sns/jscode2session")
    
    # ===========================================
    # 外部服务配置
    # ===========================================
    PDF_SERVICE_URL: str = os.getenv("PDF_SERVICE_URL", "http://localhost:13000")
    IDPHOTO_SERVICE_URL: str = os.getenv("IDPHOTO_SERVICE_URL", "http://127.0.0.1:8080")
    
    # ===========================================
    # 文件存储配置
    # ===========================================
    STATIC_FILES_DIR: str = os.getenv("STATIC_FILES_DIR", "static")
    TEMP_FILES_DIR: str = os.getenv("TEMP_FILES_DIR", "temp")
    TEMP_FILES_EXPIRE_HOURS: int = int(os.getenv("TEMP_FILES_EXPIRE_HOURS", "24"))

    # ===========================================
    # 管理员配置
    # ===========================================
    ADMIN_API_KEY: str = os.getenv("ADMIN_API_KEY", "")
    DEFAULT_ADMIN_OPENID: str = os.getenv("DEFAULT_ADMIN_OPENID", "")
    
    # ===========================================
    # CORS配置
    # ===========================================
    CORS_ORIGINS: List[str] = os.getenv("CORS_ORIGINS", "*").split(",")
    CORS_CREDENTIALS: bool = os.getenv("CORS_CREDENTIALS", "true").lower() == "true"
    CORS_METHODS: List[str] = os.getenv("CORS_METHODS", "*").split(",")
    CORS_HEADERS: List[str] = os.getenv("CORS_HEADERS", "*").split(",")
    
    # ===========================================
    # 性能配置
    # ===========================================
    REQUEST_BODY_SIZE_LIMIT: str = os.getenv("REQUEST_BODY_SIZE_LIMIT", "50mb")
    CLEANUP_INTERVAL_HOURS: int = int(os.getenv("CLEANUP_INTERVAL_HOURS", "1"))
    
    # ===========================================
    # 日志配置
    # ===========================================
    LOG_LEVEL: str = os.getenv("LOG_LEVEL", "WARNING")
    LOG_FILE: str = os.getenv("LOG_FILE", "logs/app.log")


# 创建设置实例
settings = FastAPISettings()

def validate_config():
    """验证必要的配置项"""
    warnings = []
    errors = []

    # 检查必需的配置项
    if not settings.SECRET_KEY:
        errors.append("错误: SECRET_KEY 未设置")
    elif settings.SECRET_KEY == "your-secret-key-change-in-production":
        warnings.append("警告: 请在生产环境中更改 SECRET_KEY")

    if not settings.DATABASE_URL:
        errors.append("错误: DATABASE_URL 未设置")

    # 检查可选但重要的配置项
    if not settings.WECHAT_APP_ID:
        warnings.append("警告: WECHAT_APP_ID 未设置，微信登录功能将不可用")
    if not settings.WECHAT_APP_SECRET:
        warnings.append("警告: WECHAT_APP_SECRET 未设置，微信登录功能将不可用")

    # 检查环境配置
    if settings.DEBUG:
        warnings.append("警告: 环境 DEBUG 模式")

    # 输出错误和警告
    for error in errors:
        print(f"❌ {error}")
    for warning in warnings:
        print(f"⚠️  {warning}")
    

    if errors:
        print("❌ 配置验证失败，存在必需配置项未设置")
        return False
    elif warnings:
        print("⚠️  配置验证通过，但存在警告")
        return True
    else:
        print("✅ 配置验证通过")
        return True

if __name__ == "__main__":
    validate_config()
    print(f"FastAPI服务配置加载完成")

"""
FastAPI服务配置文件
统一管理FastAPI相关的所有配置项
"""
import os
from typing import List
from dotenv import load_dotenv

# 加载环境变量
load_dotenv(dotenv_path=os.path.join(os.path.dirname(__file__), '../', '.env'))

class FastAPISettings:
    """FastAPI服务设置"""

    # ===========================================
    # 应用基本配置
    # ===========================================
    APP_NAME: str = os.getenv("APP_NAME", "Resume Service API")
    VERSION: str = os.getenv("VERSION", "1.0.0")
    DEBUG: bool = os.getenv("DEBUG", "false").lower() == "true"

    # ===========================================
    # 服务器配置
    # ===========================================
    HOST: str = os.getenv("FASTAPI_HOST", "0.0.0.0")
    PORT: int = int(os.getenv("PORT", "18080"))
    FASTAPI_PORT: int = int(os.getenv("FASTAPI_PORT", "18080"))
    WORKERS: int = int(os.getenv("WORKERS", "2"))

    # ===========================================
    # 数据库配置
    # ===========================================
    DATABASE_URL: str = os.getenv(
        "DATABASE_URL",
        "mysql+pymysql://resume_user:Resume123!@localhost/resume_service"
    )
    
    # ===========================================
    # JWT配置
    # ===========================================
    SECRET_KEY: str = os.getenv("SECRET_KEY", "your-secret-key-change-in-production")
    ALGORITHM: str = os.getenv("ALGORITHM", "HS256")
    ACCESS_TOKEN_EXPIRE_MINUTES: int = int(os.getenv("ACCESS_TOKEN_EXPIRE_MINUTES", "30"))
    
    # ===========================================
    # 微信小程序配置
    # ===========================================
    WECHAT_APP_ID: str = os.getenv("WECHAT_APP_ID")
    WECHAT_APP_SECRET: str = os.getenv("WECHAT_APP_SECRET")
    WECHAT_API_URL: str = os.getenv("WECHAT_API_URL", "https://api.weixin.qq.com/sns/jscode2session")
    
    # ===========================================
    # 外部服务配置
    # ===========================================
    PDF_SERVICE_URL: str = os.getenv("PDF_SERVICE_URL", "http://localhost:13000")
    IDPHOTO_SERVICE_URL: str = os.getenv("IDPHOTO_SERVICE_URL", "http://127.0.0.1:8080")
    
    # ===========================================
    # 文件存储配置
    # ===========================================
    STATIC_FILES_DIR: str = os.getenv("STATIC_FILES_DIR", "static")
    TEMP_FILES_DIR: str = os.getenv("TEMP_FILES_DIR", "temp")
    TEMP_FILES_EXPIRE_HOURS: int = int(os.getenv("TEMP_FILES_EXPIRE_HOURS", "24"))

    # ===========================================
    # 管理员配置
    # ===========================================
    ADMIN_API_KEY: str = os.getenv("ADMIN_API_KEY", "")
    DEFAULT_ADMIN_OPENID: str = os.getenv("DEFAULT_ADMIN_OPENID", "")
    
    # ===========================================
    # CORS配置
    # ===========================================
    CORS_ORIGINS: List[str] = os.getenv("CORS_ORIGINS", "*").split(",")
    CORS_CREDENTIALS: bool = os.getenv("CORS_CREDENTIALS", "true").lower() == "true"
    CORS_METHODS: List[str] = os.getenv("CORS_METHODS", "*").split(",")
    CORS_HEADERS: List[str] = os.getenv("CORS_HEADERS", "*").split(",")
    
    # ===========================================
    # 性能配置
    # ===========================================
    REQUEST_BODY_SIZE_LIMIT: str = os.getenv("REQUEST_BODY_SIZE_LIMIT", "50mb")
    CLEANUP_INTERVAL_HOURS: int = int(os.getenv("CLEANUP_INTERVAL_HOURS", "1"))
    
    # ===========================================
    # 日志配置
    # ===========================================
    LOG_LEVEL: str = os.getenv("LOG_LEVEL", "WARNING")
    LOG_FILE: str = os.getenv("LOG_FILE", "logs/app.log")


# 创建设置实例
settings = FastAPISettings()

def validate_config():
    """验证必要的配置项"""
    warnings = []
    errors = []

    # 检查必需的配置项
    if not settings.SECRET_KEY:
        errors.append("错误: SECRET_KEY 未设置")
    elif settings.SECRET_KEY == "your-secret-key-change-in-production":
        warnings.append("警告: 请在生产环境中更改 SECRET_KEY")

    if not settings.DATABASE_URL:
        errors.append("错误: DATABASE_URL 未设置")

    # 检查可选但重要的配置项
    if not settings.WECHAT_APP_ID:
        warnings.append("警告: WECHAT_APP_ID 未设置，微信登录功能将不可用")
    if not settings.WECHAT_APP_SECRET:
        warnings.append("警告: WECHAT_APP_SECRET 未设置，微信登录功能将不可用")

    # 检查环境配置
    if settings.DEBUG:
        warnings.append("警告: 环境 DEBUG 模式")

    # 输出错误和警告
    for error in errors:
        print(f"❌ {error}")
    for warning in warnings:
        print(f"⚠️  {warning}")
    

    if errors:
        print("❌ 配置验证失败，存在必需配置项未设置")
        return False
    elif warnings:
        print("⚠️  配置验证通过，但存在警告")
        return True
    else:
        print("✅ 配置验证通过")
        return True

if __name__ == "__main__":
    validate_config()
    print(f"FastAPI服务配置加载完成")
