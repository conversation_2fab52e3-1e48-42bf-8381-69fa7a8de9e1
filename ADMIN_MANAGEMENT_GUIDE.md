# 管理员权限管理指南

本文档介绍如何配置和使用管理员权限管理功能。

## 📋 目录

1. [功能概述](#功能概述)
2. [配置说明](#配置说明)
3. [数据库迁移](#数据库迁移)
4. [管理员管理工具](#管理员管理工具)
5. [API使用指南](#api使用指南)
6. [安全注意事项](#安全注意事项)

## 🎯 功能概述

### 管理员权限功能

- **双重鉴权机制**：支持API密钥和用户角色两种鉴权方式
- **管理员专用接口**：用户行为统计、反馈管理、系统配置等
- **权限控制**：自动验证管理员权限，拒绝未授权访问
- **管理工具**：命令行工具管理管理员用户和API密钥

### 需要管理员权限的功能

1. **用户行为分析**：
   - `/user/action/admin/stats` - 全局操作统计
2. **反馈管理**：
   - `/feedback/{id}/reply` - 回复用户反馈
3. **系统配置**：
   - `/payment/config/wechat-pay` - 获取微信支付配置
   - `/payment/config/wechat-pay` (POST) - 更新微信支付配置
   - `/payment/config/wechat-pay/validate` - 验证微信支付配置
   - `/payment/config/wechat-pay/upload-cert` - 证书上传
   - `/payment/config/environment` - 环境信息
4. **健康检查**：
   - `/health/payment-config` - 支付配置检查
   - `/health/config-summary` - 配置摘要
5. **安全监控**：
   - `/payment-security/statistics` - 安全统计
6. **错误监控**：
   - `/error/stats` - 错误统计信息
   - `/error` - 错误报告列表
7. **模板管理**：
   - `/free-templates/stats/summary` - 模板统计信息

## ⚙️ 配置说明

### 1. 环境变量配置

在 `.env` 文件中添加以下配置：

```env
# 管理员配置
ADMIN_API_KEY=your-secure-admin-api-key-change-in-production
DEFAULT_ADMIN_OPENID=
```

**配置说明：**
- `ADMIN_API_KEY`：管理员API密钥，用于外部工具访问
- `DEFAULT_ADMIN_OPENID`：默认管理员用户的openid（可选）

### 2. 生成安全的API密钥

使用管理工具生成安全的API密钥：

```bash
python admin_management.py generate-key
```

将生成的密钥添加到 `.env` 文件中。

## 🗄️ 数据库迁移

### 添加管理员字段

运行迁移脚本为用户表添加 `is_admin` 字段：

```bash
python migrations/add_admin_field.py migrate
```

### 回滚迁移（如需要）

```bash
python migrations/add_admin_field.py rollback
```

## 🛠️ 管理员管理工具

使用 `admin_management.py` 工具管理管理员用户：

### 基本命令

```bash
# 查看帮助
python admin_management.py

# 列出所有用户
python admin_management.py list

# 显示用户统计
python admin_management.py stats

# 搜索用户
python admin_management.py search 张三

# 设置用户为管理员
python admin_management.py promote oxxxxxxxxxxxxxx

# 取消用户管理员权限
python admin_management.py demote oxxxxxxxxxxxxxx

# 列出所有管理员
python admin_management.py admins

# 生成新的API密钥
python admin_management.py generate-key
```

### 使用示例

```bash
# 1. 查看当前用户状态
python admin_management.py stats

# 2. 搜索要设置为管理员的用户
python admin_management.py search admin

# 3. 设置用户为管理员
python admin_management.py promote oxxxxxxxxxxxxxx

# 4. 验证设置结果
python admin_management.py admins
```

## 🔌 API使用指南

### 鉴权方式

#### 方式1：API密钥鉴权（推荐用于外部工具）

在请求头中添加API密钥：

```bash
curl -X GET "http://localhost:18080/user/action/admin/stats" \
  -H "X-Admin-Key: your-admin-api-key"
```

#### 方式2：用户角色鉴权（用于管理员用户登录）

使用管理员用户的JWT token：

```bash
curl -X GET "http://localhost:18080/user/action/admin/stats" \
  -H "Authorization: Bearer your-jwt-token"
```

### 管理员接口示例

#### 1. 获取用户行为统计

```bash
# 使用API密钥
curl -X GET "http://localhost:18080/user/action/admin/stats?days=7&limit=50" \
  -H "X-Admin-Key: your-admin-api-key"

# 使用JWT token
curl -X GET "http://localhost:18080/user/action/admin/stats?days=7&limit=50" \
  -H "Authorization: Bearer your-jwt-token"
```

#### 2. 回复用户反馈

```bash
curl -X POST "http://localhost:18080/feedback/123/reply" \
  -H "X-Admin-Key: your-admin-api-key" \
  -H "Content-Type: application/json" \
  -d '{
    "reply_content": "感谢您的反馈，我们会及时处理。",
    "admin_name": "客服小王"
  }'
```

#### 3. 获取系统环境信息

```bash
curl -X GET "http://localhost:18080/payment-config/environment" \
  -H "X-Admin-Key: your-admin-api-key"
```

#### 4. 获取错误统计

```bash
curl -X GET "http://localhost:18080/error/stats?days=7" \
  -H "X-Admin-Key: your-admin-api-key"
```

#### 5. 获取模板统计

```bash
curl -X GET "http://localhost:18080/free-templates/stats/summary" \
  -H "X-Admin-Key: your-admin-api-key"
```

#### 6. 获取微信支付配置

```bash
curl -X GET "http://localhost:18080/payment/config/wechat-pay" \
  -H "X-Admin-Key: your-admin-api-key"
```

### 错误响应

权限验证失败时返回：

```json
{
  "detail": "需要管理员权限"
}
```

HTTP状态码：`403 Forbidden`

## 🔒 安全注意事项

### 1. API密钥安全

- **生成强密钥**：使用工具生成32位随机密钥
- **定期更换**：建议定期更换API密钥
- **安全存储**：不要在代码中硬编码密钥
- **环境隔离**：生产环境和测试环境使用不同密钥

### 2. 管理员用户管理

- **最小权限原则**：只给必要的用户管理员权限
- **定期审查**：定期检查管理员用户列表
- **操作记录**：重要操作会记录在日志中
- **权限回收**：及时回收离职人员的管理员权限

### 3. 网络安全

- **HTTPS传输**：生产环境必须使用HTTPS
- **IP白名单**：可考虑限制管理员API的访问IP
- **请求频率限制**：防止暴力破解攻击
- **日志监控**：监控管理员API的访问日志

### 4. 配置安全

```env
# 生产环境配置示例
ADMIN_API_KEY=AbCdEfGhIjKlMnOpQrStUvWxYz123456  # 32位随机密钥
DEFAULT_ADMIN_OPENID=                            # 生产环境建议留空
```

## 🚀 部署建议

### 1. 初始化管理员

```bash
# 1. 运行数据库迁移
python migrations/add_admin_field.py migrate

# 2. 生成API密钥
python admin_management.py generate-key

# 3. 更新.env文件
# 将生成的密钥添加到ADMIN_API_KEY

# 4. 设置初始管理员（可选）
python admin_management.py promote your-admin-openid

# 5. 重启服务
```

### 2. 验证配置

```bash
# 测试API密钥访问
curl -X GET "http://localhost:18080/user/action/admin/stats" \
  -H "X-Admin-Key: your-admin-api-key"

# 应该返回统计数据而不是403错误
```

## 📞 技术支持

如果在使用过程中遇到问题：

1. 检查日志文件中的错误信息
2. 验证配置文件是否正确
3. 确认数据库迁移是否成功
4. 检查API密钥是否正确配置

---

**注意**：管理员权限涉及系统安全，请严格按照本文档进行配置和使用。
